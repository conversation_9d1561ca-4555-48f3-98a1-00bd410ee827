import React, { useMemo, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';
import './Dashboard.css';
import { FaBriefcase } from "react-icons/fa";
import { PiChartLineUp } from "react-icons/pi";
import { ImProfile } from "react-icons/im";
import { BsEnvelope } from "react-icons/bs";
import { useTheme } from '../../contexts/ThemeContext';

const Dashboard = () => {
  const { isDarkMode } = useTheme();

  // Registrar tema escuro personalizado para ECharts
  useEffect(() => {
    // Registrar tema escuro customizado
    echarts.registerTheme('dark', {
      backgroundColor: 'transparent',
      textStyle: {
        color: '#ffffff'
      },
      title: {
        textStyle: {
          color: '#ffffff'
        }
      },
      legend: {
        textStyle: {
          color: '#ffffff'
        }
      },
      categoryAxis: {
        axisLine: {
          lineStyle: {
            color: '#333333'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#333333'
          }
        },
        axisLabel: {
          color: '#b3b3b3'
        },
        splitLine: {
          lineStyle: {
            color: '#333333'
          }
        }
      },
      valueAxis: {
        axisLine: {
          lineStyle: {
            color: '#333333'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#333333'
          }
        },
        axisLabel: {
          color: '#b3b3b3'
        },
        splitLine: {
          lineStyle: {
            color: '#333333'
          }
        }
      }
    });

    // Registrar tema claro personalizado
    echarts.registerTheme('light', {
      backgroundColor: 'transparent',
      textStyle: {
        color: '#333333'
      },
      title: {
        textStyle: {
          color: '#333333'
        }
      },
      legend: {
        textStyle: {
          color: '#333333'
        }
      },
      categoryAxis: {
        axisLine: {
          lineStyle: {
            color: '#cccccc'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#cccccc'
          }
        },
        axisLabel: {
          color: '#666666'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      valueAxis: {
        axisLine: {
          lineStyle: {
            color: '#cccccc'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#cccccc'
          }
        },
        axisLabel: {
          color: '#666666'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      }
    });
  }, []);

  // Cores dinâmicas baseadas no tema
  const colors = useMemo(() => ({
    text: isDarkMode ? '#ffffff' : '#333333',
    textSecondary: isDarkMode ? '#b3b3b3' : '#666666',
    background: isDarkMode ? '#1e1e1e' : '#ffffff',
    gridLine: isDarkMode ? '#333333' : '#f0f0f0',
    tooltipBg: isDarkMode ? '#2a2a2a' : '#ffffff',
    tooltipBorder: isDarkMode ? '#444444' : '#cccccc'
  }), [isDarkMode]);

  // Dados fictícios para os KPIs
  const kpis = [
    {
      title: 'Realizado',
      value: '86',
      icon: <PiChartLineUp color='white'/>,
      color: '#8B5CF6',
      bgColor: '#F3F4F6'
    },
    {
      title: 'Aplicado',
      value: '75',
      icon: <FaBriefcase color='white'/>,
      color: '#06B6D4',
      bgColor: '#F0F9FF'
    },
    {
      title: 'Perfil',
      value: '45',
      icon: <ImProfile color='white'/>,
      color: '#10B981',
      bgColor: '#F0FDF4'
    },
    {
      title: 'Mensagens',
      value: '93',
      icon: <BsEnvelope color='white'/>,
      color: '#84CC16',
      bgColor: '#F7FEE7'
    }
  ];

  // Configuração do gráfico de barras
  const barChartOption = useMemo(() => ({
    backgroundColor: 'transparent',
    textStyle: {
      color: colors.text
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: colors.tooltipBg,
      borderColor: colors.tooltipBorder,
      textStyle: {
        color: colors.text
      },
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach(function (item) {
          result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' 
            + item.color + '"></span>' 
            + item.seriesName + ': ' + item.value + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['Vendas', 'Meta'],
      right: 'right',
      top: 10,
      textStyle: {
        color: colors.text
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: colors.textSecondary
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: colors.textSecondary
      },
      splitLine: {
        lineStyle: {
          color: colors.gridLine
        }
      }
    },
    series: [
      {
        name: 'Vendas',
        type: 'bar',
        data: [220, 182, 191, 234, 290, 330, 310, 282, 191, 234, 290, 330],
        itemStyle: {
          color: '#10B981'
        },
        barWidth: '40%'
      },
      {
        name: 'Meta',
        type: 'bar',
        data: [150, 232, 201, 154, 190, 330, 410, 323, 242, 334, 390, 430],
        itemStyle: {
          color: '#8B5CF6'
        },
        barWidth: '40%'
      }
    ]
  }), [colors]);

  // Configuração do gráfico de linha (Dados)
  const lineChartOption = useMemo(() => ({
    backgroundColor: 'transparent',
    textStyle: {
      color: colors.text
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: colors.tooltipBg,
      borderColor: colors.tooltipBorder,
      textStyle: {
        color: colors.text
      }
    },
    legend: {
      data: ['Enviado', 'Realizado', 'Rejeitado'],
      right: 'right',
      top: 10,
      textStyle: {
        color: colors.text
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['Dia 01', 'Dia 02', 'Dia 03', 'Dia 04', 'Dia 05', 'Dia 06', 'Dia 07', 'Dia 08', 'Dia 09', 'Dia 10'],
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: colors.textSecondary
      }
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: colors.textSecondary
      },
      splitLine: {
        lineStyle: {
          color: colors.gridLine
        }
      }
    },
    series: [
      {
        name: 'Enviado',
        type: 'line',
        data: [80, 82, 85, 90, 95, 80, 85, 75, 70, 65],
        smooth: true,
        lineStyle: {
          color: '#3B82F6',
          width: 3
        },
        itemStyle: {
          color: '#3B82F6'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
            ]
          }
        }
      },
      {
        name: 'Realizado',
        type: 'line',
        data: [30, 35, 42, 48, 40, 38, 35, 30, 25, 20],
        smooth: true,
        lineStyle: {
          color: '#10B981',
          width: 3
        },
        itemStyle: {
          color: '#10B981'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
              { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
            ]
          }
        }
      },
      {
        name: 'Rejeitado',
        type: 'line',
        data: [10, 8, 6, 5, 7, 9, 12, 15, 18, 20],
        smooth: true,
        lineStyle: {
          color: '#EF4444',
          width: 3
        },
        itemStyle: {
          color: '#EF4444'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
              { offset: 1, color: 'rgba(239, 68, 68, 0.05)' }
            ]
          }
        }
      }
    ]
  }), [colors]);

  return (
    <div className="dashboard-container">
      {/* KPIs Row */}
      <div className="kpis-row">
        {kpis.map((kpi, index) => (
          <div key={index} className="kpi-card" style={{ '--kpi-color': kpi.color }}>
            <div className="kpi-icon">{kpi.icon}</div>
            <div className="kpi-content">
              <h3 className="kpi-title">{kpi.title}</h3>
              <div className="kpi-value">{kpi.value}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="charts-row">
        {/* Bar Chart */}
        <div className="chart-container">
          <div className="chart-title">Mensal</div>
                      <ReactECharts 
              option={barChartOption} 
              style={{ height: '480px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
              lazyUpdate={false}
              theme={isDarkMode ? 'dark' : 'light'}
            />
          </div>

          {/* Line Chart */}
          <div className="chart-container">
            <div className="chart-title">Dados</div>
            <ReactECharts 
              option={lineChartOption} 
              style={{ height: '480px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
              lazyUpdate={false}
              theme={isDarkMode ? 'dark' : 'light'}
            />
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 