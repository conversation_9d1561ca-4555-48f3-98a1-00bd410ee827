const { expressjwt: jwt } = require('express-jwt');
const jwksRsa = require('jwks-rsa');
const jwtAuthz = require('express-jwt-authz');

// Middleware para verificar o token JWT
const checkJwt = jwt({
    secret: jwksRsa.expressJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`
    }),
    audience: process.env.AUTH0_AUDIENCE,
    issuer: `https://${process.env.AUTH0_DOMAIN}/`,
    algorithms: ['RS256']
});

// Middleware para verificar permissões de admin
const checkAdminPermission = (req, res, next) => {
    const permissions = req.auth.permissions || [];
    if (permissions.includes('admin:all')) {
        next();
    } else {
        res.status(403).json({ error: 'Acesso negado. Permissão de administrador necessária.' });
    }
};

module.exports = {
    checkJwt,
    checkAdminPermission
};