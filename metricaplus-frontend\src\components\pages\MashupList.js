import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import EnterpriseLoginModal from '../../modais/EnterpriseLoginModal';
import defaultBackground from '../../assets/img/default-bg.jpg';
import './MashupList.css';

// Chave para o cache no localStorage
const CACHE_KEY = 'metricaplus_mashups_cache';
const CACHE_TIMESTAMP_KEY = 'metricaplus_mashups_timestamp';

// Componente de Spinner de Carregamento
const LoadingSpinner = () => {
  return (
    <div className="loading-container">
      <div className="loading-spinner"></div>
      <p className="loading-text">Carregando...</p>
    </div>
  );
};

const MashupCard = ({ mashup }) => {
  // Função para gerar a URL correta para o mashup
  const getMashupUrl = () => {
    if (mashup.type === 'qlikSense') {
      if (mashup.dummy) {
        // Acessar o Hub do Qlik Sense diretamente
        return `${mashup.endpoint}/hub/`;
      }
      // Para mashups normais do Qlik Sense
      return `${mashup.endpoint}${mashup.path}`;
    } else {
      // Para mashups locais
      return `http://${window.location.hostname}:80${mashup.path}`;
    }
  };

  const renderIcon = () => {
    if (mashup.type === 'qlikSense') {
      if (mashup.dummy) {
        return (
          <svg className="mashup-icon qlik-hub" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
            <rect x="7" y="7" width="3" height="9" />
            <rect x="14" y="7" width="3" height="5" />
          </svg>
        );
      } else {
        return (
          <svg className="mashup-icon qlik-mashup" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polygon points="12 2 2 7 12 12 22 7 12 2" />
            <polyline points="2 17 12 22 22 17" />
            <polyline points="2 12 12 17 22 12" />
          </svg>
        );
      }
    } else {
      return (
        <svg className="mashup-icon cloud-mashup" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z" />
        </svg>
      );
    }
  };

  const url = getMashupUrl();
  const backgroundImage = mashup.preview || defaultBackground;
  // Criando uma classe de estilo dinâmica para o background
  const cardStyle = {
    '--card-bg-image': `url(${backgroundImage})`
  };

  return (
    <div className="mashup-card" style={cardStyle}>
      <div className="mashup-card-content">
        <div className="mashup-icon-container">
          {renderIcon()}
        </div>

        <div className="mashup-text-group">
          <h3 className="mashup-title">{mashup.name}</h3>
          <p className="mashup-description">
            {mashup.type === 'qlikSense'
              ? mashup.dummy
                ? 'Hub do Qlik Sense Enterprise'
                : 'Mashup do Qlik Sense Enterprise'
              : 'Mashup Cloud'}
          </p>
          <span className="mashup-url">{url}</span>
        </div>

        <a
          href={url}
          className="mashup-link-button"
          target="_blank"
          rel="noopener noreferrer"
        >
          <span>Acessar</span>
          <svg className="arrow-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="5" y1="12" x2="19" y2="12" />
            <polyline points="12 5 19 12 12 19" />
          </svg>
        </a>
      </div>
    </div>
  );
};

const MashupList = () => {
  const { getAccessTokenSilently } = useAuth0();
  const [mashups, setMashups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [showEnterpriseLogin, setShowEnterpriseLogin] = useState(false);
  const [enterpriseCredentials, setEnterpriseCredentials] = useState(null);
  const [loginError, setLoginError] = useState(null);
  const [credentialsLoaded, setCredentialsLoaded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const storedCredentials = localStorage.getItem('enterpriseCredentials');
    if (storedCredentials) {
      setEnterpriseCredentials(JSON.parse(storedCredentials));
    }
    setCredentialsLoaded(true);

    // Carregar dados do cache ao montar o componente
    const cachedData = localStorage.getItem(CACHE_KEY);
    const cachedTimestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);

    if (cachedData && cachedTimestamp) {
      const now = new Date().getTime();
      const timestamp = parseInt(cachedTimestamp);
      console.log("loading mashups::::", now - timestamp);
      // Usar cache se tiver menos de 1 hora
      if (now - timestamp < 3600000) {
        setMashups(JSON.parse(cachedData));
        setLoading(false);
      }
      else {
        fetchMashups();
      }
    }
  }, []);

  const username = enterpriseCredentials?.username || undefined;
  const password = enterpriseCredentials?.password || undefined;

  const fetchMashups = async (isRefreshing = false) => {
    try {
      if (isRefreshing) {
        setRefreshing(true);
      }

      console.log("fetching mashups::::");

      const token = await getAccessTokenSilently({
        audience: process.env.REACT_APP_AUTH0_AUDIENCE,
        scope: 'read:mashups'
      });
      console.log('passou aqui?');
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/mashups`, {
        params: {
          username,
          password
        },
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('passou aqui2?');
      if (response.data.requiresEnterpriseLogin) {
        setLoginError(null);
        setShowEnterpriseLogin(true);
      } else {
        // Atualizar cache
        localStorage.setItem(CACHE_KEY, JSON.stringify(response.data));
        localStorage.setItem(CACHE_TIMESTAMP_KEY, new Date().getTime().toString());

        setMashups(response.data);
        setShowEnterpriseLogin(false);
      }
    } catch (error) {
      console.log('passou aqui3?', error.response);
      if (error.response?.data?.requiresEnterpriseLogin) {
        console.log('passou aqui4?');
        if (error.response.data.authFailed) {
          setLoginError(error.response.data.message);
        } else {
          setLoginError(null);
        }
        setShowEnterpriseLogin(true);
      } else {
        console.error('Erro ao buscar mashups:', error);
        setError('Não foi possível carregar a lista de mashups');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (!credentialsLoaded) return;

    // Só buscar do servidor se não tiver cache
    const cachedData = localStorage.getItem(CACHE_KEY);
    const cachedTimestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);

    if (!cachedData || !cachedTimestamp) {
      fetchMashups();
    }
  }, [getAccessTokenSilently, enterpriseCredentials, credentialsLoaded]);

  const handleRefresh = () => {
    fetchMashups(true);
  };

  const handleEnterpriseSubmit = async ({ username, password }) => {
    try {
      setLoginError(null);

      // Obter token para autenticar com o backend
      const token = await getAccessTokenSilently({
        audience: process.env.REACT_APP_AUTH0_AUDIENCE,
        scope: 'read:mashups'
      });

      // Validar credenciais primeiro
      const validationResponse = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/validate-enterprise`,
        { username, password },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Se validação for bem-sucedida, atualizar estado e localStorage
      if (validationResponse.data.success) {
        setEnterpriseCredentials({ username, password });
        localStorage.setItem('enterpriseCredentials', JSON.stringify({ username, password }));
        
        // Buscar mashups com as novas credenciais
        const mashupResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/mashups`, {
          params: { username, password },
          headers: { Authorization: `Bearer ${token}` }
        });

        // Atualizar cache e estado apenas se os mashups forem carregados com sucesso
        localStorage.setItem(CACHE_KEY, JSON.stringify(mashupResponse.data));
        localStorage.setItem(CACHE_TIMESTAMP_KEY, new Date().getTime().toString());
        setMashups(mashupResponse.data);
        
        // Fechar o modal apenas após o carregamento completo dos mashups
        setShowEnterpriseLogin(false);
      } else {
        throw new Error(validationResponse.data.message || 'Falha na validação das credenciais');
      }
    } catch (error) {
      console.error('Erro na validação das credenciais:', error);
      // Propaga o erro com os detalhes do backend
      if (error.response?.data) {
        throw error; // Propaga o erro com os detalhes do backend
      } else {
        throw new Error('Erro ao processar a requisição');
      }
    }
  };

  // Filtragem de mashups com base no termo de pesquisa
  const filteredMashups = mashups.filter(mashup =>
    mashup.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <div className="error-message">{error}</div>
      </div>
    );
  }

  return (
    <div className="mashup-container">
      {showEnterpriseLogin && (
        <EnterpriseLoginModal
          isOpen={showEnterpriseLogin}
          onClose={() => setShowEnterpriseLogin(false)}
          onSubmit={handleEnterpriseSubmit}
          errorMessage={loginError}
        />
      )}

      <div className="mashup-header-controls">
        <div className="mashup-search">
          <svg className="search-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
          </svg>
          <input
            type="text"
            placeholder="Buscar mashups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mashup-search-input"
          />
        </div>

        <div className="mashup-filters">
          <select className="mashup-filter-select">
            <option value="all">Todos os tipos</option>
            <option value="qlikSense">Qlik Sense</option>
            <option value="cloud">Cloud</option>
          </select>

          <select className="mashup-sort-select">
            <option value="nameAsc">Nome (A-Z)</option>
            <option value="nameDesc">Nome (Z-A)</option>
            <option value="recent">Mais recentes</option>
          </select>
        </div>
      </div>

      <div className="mashup-stats">
        <div className="stat-card">
          <div className="stat-number">{mashups.length}</div>
          <div className="stat-label">Total de Mashups</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{mashups.filter(m => m.type === 'qlikSense').length}</div>
          <div className="stat-label">Qlik Sense</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">{mashups.filter(m => m.type !== 'qlikSense').length}</div>
          <div className="stat-label">Cloud</div>
        </div>
      </div>

      <button
        className={`refresh-button ${refreshing ? 'loading' : ''}`}
        onClick={handleRefresh}
        disabled={refreshing}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3" />
        </svg>
        {refreshing ? 'Atualizando...' : 'Atualizar Lista'}
      </button>

      <div className="mashup-grid">
        {filteredMashups.length > 0 ? (
          filteredMashups.map((mashup, index) => (
            <MashupCard key={index} mashup={mashup} />
          ))
        ) : (
          <div className="no-mashups-card">
            <svg className="no-mashups-icon" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14 2 14 8 20 8" />
              <line x1="12" y1="18" x2="12" y2="12" />
              <line x1="9" y1="15" x2="15" y2="15" />
            </svg>
            <p className="no-mashups-text">
              {searchTerm
                ? `Nenhum mashup corresponde à pesquisa "${searchTerm}"`
                : 'Nenhum mashup disponível para seu perfil'}
            </p>
            {searchTerm && (
              <button
                className="clear-search-button"
                onClick={() => setSearchTerm('')}
              >
                Limpar pesquisa
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MashupList; 