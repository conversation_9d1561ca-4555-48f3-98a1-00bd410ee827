import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify'; // Importa o Toastify se já não tiver feito
import '../styles/EnterpriseLoginModal.css';
import { FiAlertCircle } from 'react-icons/fi';

// Componente de Login Qlik Enterprise
const EnterpriseLoginModal = ({ isOpen, onClose, onSubmit }) => {
    const [formData, setFormData] = useState({
        username: '',
        password: '',
    });
    const [validationStep, setValidationStep] = useState('idle'); // 'idle', 'validating', 'loading', 'error'
    const [error, setError] = useState('');
    const [messageOpacity, setMessageOpacity] = useState(1);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        if (error) setError('');
        if (validationStep !== 'idle') setValidationStep('idle');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');

        // Validação do formato dominio\usuario
        const usernameRegex = /^[^\\\/:*?"<>|]+\\[^\\\/:*?"<>|]+$/;
        if (!usernameRegex.test(formData.username)) {
            toast.error('Usuário inválido. Use o formato dominio\\usuario.');
            return; // Impede o envio do formulário
        }

        let timer;

        try {
            setValidationStep('validating');
            setMessageOpacity(1);

            // Timer para mudar a mensagem após 3 segundos
            timer = setTimeout(() => {
                setMessageOpacity(0);
                setTimeout(() => {
                    setValidationStep('loading');
                    setMessageOpacity(1);
                }, 300); // Tempo para a transição de fade out
            }, 3000);

            await onSubmit(formData);

            // Limpa o timer se o submit for concluído antes dos 3 segundos
            clearTimeout(timer);
        } catch (err) {
            // cancelando o timer mesmo em erro
            clearTimeout(timer);
            setValidationStep('error');
            toast.error('Erro ao fazer login. Verifique suas credenciais.');
            // Tratamento específico para erros de autenticação
            if (err.response?.data?.code === 'USER_NOT_AUTHENTICATED') {
                setError('Usuário não autenticado');
            } else if (err.response?.data?.details) {
                setError(err.response.data.details);
            } else if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('Ocorreu um erro ao tentar fazer login. Por favor, tente novamente.');
            }
        }
    };

    const getStatusMessage = () => {
        switch (validationStep) {
            case 'validating':
                return 'Validando credenciais...';
            case 'loading':
                return 'Carregando mashups...';
            default:
                return 'Entrar';
        }
    };

    if (!isOpen) return null;

    const isLoading = validationStep === 'validating' || validationStep === 'loading';

    return (
        <div className="enterprise-modal-overlay">
            <div className="enterprise-modal-container">
                <div className="enterprise-modal-header">
                    <h2 className="enterprise-modal-title">Login Empresarial</h2>
                    <p className="enterprise-modal-subtitle">
                        Entre com suas credenciais para acessar os mashups
                    </p>
                </div>

                {error && (
                    <div className="enterprise-modal-error">
                        <FiAlertCircle className="error-icon" />
                        {error}
                    </div>
                )}

                <form onSubmit={handleSubmit} className="enterprise-modal-form">
                    <div className="enterprise-modal-input-group">
                        <label htmlFor="username">Usuário</label>
                        <input
                            id="username"
                            type="text"
                            name="username"
                            placeholder="dominio\usuario"
                            className={`enterprise-modal-input ${error ? 'input-error' : ''}`}
                            value={formData.username}
                            onChange={handleInputChange}
                            disabled={isLoading}
                            required
                        />
                    </div>

                    <div className="enterprise-modal-input-group">
                        <label htmlFor="password">Senha</label>
                        <input
                            id="password"
                            type="password"
                            name="password"
                            placeholder="Sua senha do Qlik Enterprise"
                            className={`enterprise-modal-input ${error ? 'input-error' : ''}`}
                            value={formData.password}
                            onChange={handleInputChange}
                            disabled={isLoading}
                            required
                        />
                    </div>

                    <div className="enterprise-modal-actions">
                        <button
                            type="button"
                            onClick={onClose}
                            className="enterprise-modal-button cancel-button"
                            disabled={isLoading}
                        >
                            Cancelar
                        </button>
                        <button
                            type="submit"
                            className="enterprise-modal-button submit-button"
                            disabled={isLoading || !formData.username || !formData.password}
                        >
                            {isLoading && <div className="spinner-login-enterprise" />}
                            <span style={{ opacity: messageOpacity, transition: 'opacity 0.3s ease-in-out' }}>
                                {getStatusMessage()}
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EnterpriseLoginModal;