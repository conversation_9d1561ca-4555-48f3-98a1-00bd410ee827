.users-container {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
}

.users-header {
  margin-bottom: 2.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.users-header h2 {
  color: var(--text-primary);
  font-size: 2.2rem;
  margin: 0;
  font-weight: 600;
}

.create-user-form {
  background: var(--card-color);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  margin-bottom: 2.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.create-user-form:hover {
  box-shadow: var(--shadow-lg);
}

.create-user-form h3 {
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 1.8rem;
  font-size: 1.6rem;
  font-weight: 600;
  border-left: 4px solid var(--primary-color);
  padding-left: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.9rem 1.2rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: var(--background-color);
  color: var(--text-primary);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 166, 81, 0.2);
  background-color: var(--card-color);
}

.create-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.9rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 4px 6px rgba(0, 166, 81, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.create-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 166, 81, 0.2);
}

.create-button:active {
  transform: translateY(0);
}

.users-list {
  background: var(--card-color);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: box-shadow 0.2s;
}

.users-list:hover {
  box-shadow: var(--shadow-lg);
}

.users-list h3 {
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 1.8rem;
  font-size: 1.6rem;
  font-weight: 600;
  border-left: 4px solid var(--info-color);
  padding-left: 1rem;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 1.8rem;
}

.user-card {
  border: none;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s;
  position: relative;
  background: var(--card-color);
  border-top: 4px solid transparent;
}

.user-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-top-color: var(--info-color);
}

.user-info {
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
  border: 3px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: transform 0.3s;
}

.user-card:hover .user-avatar {
  transform: scale(1.05);
  border-color: var(--info-color);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-details h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.3rem;
}

.user-details p {
  margin: 0.4rem 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0 !important;
}

.user-id {
  font-size: 0.85rem !important;
  color: var(--text-light) !important;
  margin-top: 0 !important;
  background-color: var(--background-color);
  display: inline-block;
  padding: 0.2rem 0.7rem;
  border-radius: 30px;
  border: 1px solid var(--border-color);
}

.user-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.8rem;
  padding-top: 0.5rem;
  border-top: 1px solid #edf2f7;
}

.delete-button, .edit-button, .permissions-button {
  border: none;
  padding: 0.6rem 1.1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.delete-button {
  background-color: var(--card-color);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.delete-button:hover {
  background-color: #dc3545;
  color: white;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

.edit-button {
  background-color: var(--card-color);
  color: var(--info-color);
  border: 1px solid var(--info-color);
}

.edit-button:hover {
  background-color: #0d6efd;
  color: white;
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
}

.permissions-button {
  background-color: var(--card-color);
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
}

.permissions-button:hover {
  background-color: #6c5ce7;
  color: white;
  box-shadow: 0 4px 8px rgba(108, 92, 231, 0.2);
}

.error-message {
  background-color: #fff5f5;
  color: #dc3545;
  padding: 1.2rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  text-align: center;
  border: 1px solid #ffcdd2;
  font-size: 1rem;
  box-shadow: 0 4px 6px rgba(220, 53, 69, 0.08);
}

/* Estilos do Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: var(--card-color);
  padding: 1.8rem;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: var(--shadow-xl);
  animation: slideInUp 0.3s ease-out forwards;
  max-height: 90vh;
  overflow-y: auto;
}

.permissions-modal {
  max-width: 700px;
  max-height: 80vh;
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.4rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-light);
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
  transition: all 0.2s;
}

.modal-body {
  padding: 1rem 0;
}

.permissions-section {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.permissions-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.permissions-section h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.api-select {
  width: 100%;
  padding: 0.7rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  background-color: var(--background-color);
  color: var(--text-primary);
}

.api-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 166, 81, 0.2);
}

.permissions-loading, .no-permissions {
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  margin-top: 15px;
  max-height: 300px;
  min-height: 300px;
  overflow-y: auto;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 10px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--background-color);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.current-permissions, .add-permissions {
  margin-top: 1rem;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 0.8rem;
  background-color: var(--background-color);
  transition: all 0.2s;
}

.permission-item:hover {
  border-color: var(--primary-color);
  background-color: var(--card-color);
  box-shadow: var(--shadow-sm);
}

.permission-info {
  display: flex;
  flex-direction: column;
}

.permission-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.3rem;
}

.permission-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.add-permission-button, .remove-permission-button {
  padding: 0.5rem 0.8rem;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-weight: 500;
}

.add-permission-button {
  background-color: var(--card-color);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.add-permission-button:hover:not(:disabled) {
  background-color: var(--success-color);
  color: white;
}

.remove-permission-button {
  background-color: var(--card-color);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.remove-permission-button:hover:not(:disabled) {
  background-color: var(--error-color);
  color: white;
}

.add-permission-button:disabled, .remove-permission-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.close-permissions-button {
  background-color: var(--border-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 0.7rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
}

.close-permissions-button:hover {
  background-color: var(--text-light);
  color: var(--text-primary);
  border-color: var(--text-light);
}

.modal-body p {
  margin: 0 0 1rem;
  color: var(--text-secondary);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.modal-footer .delete-button {
  background-color: var(--error-color);
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: background-color 0.2s;
  box-shadow: 0 4px 6px rgba(229, 62, 62, 0.1);
}

.modal-footer .delete-button:hover:not(:disabled) {
  background-color: var(--error-dark);
  box-shadow: 0 6px 10px rgba(229, 62, 62, 0.2);
}

.modal-footer .delete-button:active:not(:disabled) {
  transform: scale(0.98);
}

.modal-footer .delete-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 4px 6px rgba(0, 166, 81, 0.1);
}

.save-button:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 6px 10px rgba(0, 166, 81, 0.2);
}

.save-button:active {
  transform: scale(0.98);
}

.cancel-button {
  background-color: var(--card-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.7rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
  border-color: var(--text-light);
}

.cancel-button:active {
  transform: scale(0.98);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--card-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--background-color);
  color: var(--text-primary);
  border-color: var(--primary-color);
}

.pagination-button:active:not(:disabled) {
  transform: scale(0.98);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s;
}

.pagination-button:hover:not(:disabled) svg {
  transform: translateX(-3px);
}

.pagination-info {
  color: var(--text-primary);
  font-size: 0.95rem;
  background-color: var(--card-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.users-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.users-list-header h3 {
  margin-bottom: 0;
}

.search-container {
  position: relative;
  --width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.7rem 1rem 0.7rem 2.5rem;
  --border: 1px solid #e2e8f0;
  --border-radius: 8px;
  font-size: 0.95rem;
  --background-color: #f8fafc;
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  --border-color: #4299e1;
  --box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
  --background-color: #fff;
}

.search-input::placeholder {
  --color: #a0aec0;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  transition: color 0.3s;
  pointer-events: none;
  width: 16px;
  height: 16px;
}

.search-input:focus + .search-icon {
  color: #4299e1;
}

.permissions-list {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.permission-checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: #f8fafc;
  transition: all 0.2s;
}

.permission-checkbox-item:hover {
  border-color: #cbd5e0;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.permission-checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.permission-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.permission-label {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  flex: 1;
}

.permission-label .permission-name {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.2rem;
}

.permission-label .permission-description {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.permission-status {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  white-space: nowrap;
}

.permission-active {
  color: var(--success-color);
  background-color: rgba(56, 161, 105, 0.1);
  border: 1px solid var(--success-color);
}

.permission-inactive {
  color: var(--text-light);
  background-color: rgba(113, 128, 150, 0.1);
  border: 1px solid var(--text-light);
}

.permissions-list-container {
  margin-top: 15px;
  max-height: 300px;
  min-height: 300px;
  overflow-y: auto;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 10px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--background-color);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.permissions-list-container::-webkit-scrollbar {
  width: 8px;
}

.permissions-list-container::-webkit-scrollbar-track {
  background: var(--background-color);
  border-radius: 4px;
}

.permissions-list-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.permissions-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

.permission-checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--card-color);
  transition: all 0.2s;
}

.permission-checkbox-item:hover {
  border-color: var(--primary-color);
  background-color: var(--background-color);
  box-shadow: var(--shadow-sm);
}

.permission-checkbox:checked + .permission-label .permission-name {
  color: var(--primary-color);
  font-weight: 600;
}

.permission-checkbox-container input[type="checkbox"] {
  position: relative;
  transition: all 0.2s;
}

.permission-checkbox-container input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.4);
}

.permission-active {
  color: var(--success-color);
  background-color: rgba(56, 161, 105, 0.1);
  border: 1px solid var(--success-color);
  transition: all 0.3s ease;
}

.permission-inactive {
  color: var(--text-light);
  background-color: rgba(113, 128, 150, 0.1);
  border: 1px solid var(--text-light);
  transition: all 0.3s ease;
}

.permissions-search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.permissions-search-container h4 {
  margin: 0;
  color: var(--text-primary);
}

.permissions-search-input-container {
  position: relative;
  width: 250px;
}

.permissions-search-input {
  width: 100%;
  padding: 0.6rem 1rem 0.6rem 2.2rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  background-color: var(--card-color);
  color: var(--text-primary);
  transition: all 0.3s;
}

.permissions-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 166, 81, 0.2);
  background-color: var(--background-color);
}

.permissions-search-icon {
  position: absolute;
  left: 0.7rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  width: 14px;
  height: 14px;
  pointer-events: none;
}

.permissions-search-input:focus + .permissions-search-icon {
  color: var(--primary-color);
}

.permissions-search-input::placeholder {
  color: var(--text-light);
} 