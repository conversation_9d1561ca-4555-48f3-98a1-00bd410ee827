require('dotenv').config();
const { ManagementClient } = require('auth0');

// Configuração do Auth0 Management API
const auth0Management = new ManagementClient({
    domain: process.env.AUTH0_DOMAIN,
    clientId: process.env.AUTH0_MANAGEMENT_CLIENT_ID,
    clientSecret: process.env.AUTH0_MANAGEMENT_CLIENT_SECRET,
    scope: 'read:users write:users delete:users',
    audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`
});

module.exports = { auth0Management };