const winston = require('winston');
const path = require('path');

// Configuração dos formatos de log
const formats = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
);

// Configuração dos transportes (onde os logs serão salvos)
const transports = [
    // Console para desenvolvimento
    new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.printf(({ level, message, timestamp, ...metadata }) => {
                let msg = `[${timestamp}] ${level}: ${message}`;
                if (Object.keys(metadata).length > 0) {
                    msg += ` ${JSON.stringify(metadata)}`;
                }
                return msg;
            })
        )
    }),
    // Arquivo para todos os logs
    new winston.transports.File({
        filename: path.join('logs', 'combined.log'),
        format: formats
    }),
    // Arquivo separado para erros
    new winston.transports.File({
        filename: path.join('logs', 'error.log'),
        level: 'error',
        format: formats
    })
];

// Criação do logger
const logger = winston.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: formats,
    transports,
    // Não sair em caso de erro
    exitOnError: false
});

// Funções auxiliares para logging
const logInfo = (message, data = null) => {
    if (data) {
        logger.info(message, { data });
    } else {
        logger.info(message);
    }
};

const logWarning = (message, data = null) => {
    if (data) {
        logger.warn(message, { data });
    } else {
        logger.warn(message);
    }
};

const logError = (message, error = null) => {
    if (error) {
        logger.error(message, {
            error: {
                message: error.message,
                stack: error.stack,
                ...(error.response && {
                    response: {
                        status: error.response.status,
                        data: error.response.data
                    }
                })
            }
        });
    } else {
        logger.error(message);
    }
};

const logSuccess = (message, data = null) => {
    if (data) {
        logger.info(`✅ ${message}`, { data });
    } else {
        logger.info(`✅ ${message}`);
    }
};

// Função para logging de requisições HTTP
const logRequest = (req, res, next) => {
    const start = Date.now();
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);

    // Adiciona o ID da requisição ao objeto req
    req.requestId = requestId;

    // Log da requisição recebida
    logInfo(`[${requestId}] Nova requisição: ${req.method} ${req.originalUrl}`, {
        headers: req.headers,
        query: req.query,
        body: req.body
    });

    // Intercepta a resposta
    res.on('finish', () => {
        const duration = Date.now() - start;
        logInfo(`[${requestId}] Resposta enviada: ${res.statusCode} (${duration}ms)`);
    });

    next();
};

// Função para logging de erros
const logErrorHandler = (err, req, res, next) => {
    const requestId = req.requestId || 'unknown';
    logError(`[${requestId}] Erro não tratado:`, err);
    next(err);
};

module.exports = {
    logger,
    logInfo,
    logWarning,
    logError,
    logSuccess,
    logRequest,
    logErrorHandler
};
