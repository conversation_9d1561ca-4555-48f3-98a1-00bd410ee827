import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import './Settings.css';

const Settings = () => {
  const { isDarkMode, toggleDarkMode } = useTheme();

  const ToggleIcon = ({ isDark }) => (
    <div className="toggle-icon">
      {isDark ? (
        // <PERSON><PERSON><PERSON> da lua (dark mode)
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
        </svg>
      ) : (
        // Ícone do sol (light mode)
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="5"/>
          <line x1="12" y1="1" x2="12" y2="3"/>
          <line x1="12" y1="21" x2="12" y2="23"/>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
          <line x1="1" y1="12" x2="3" y2="12"/>
          <line x1="21" y1="12" x2="23" y2="12"/>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
        </svg>
      )}
    </div>
  );

  return (
    <div className="settings-container">
      <div className="settings-content">
        <div className="settings-header">
          <h1>Configurações</h1>
          <p>Configure suas preferências e personalize sua experiência</p>
        </div>

        <div className="settings-sections">
          {/* Seção de Aparência */}
          <div className="settings-section">
            <div className="section-header">
              <h2>Aparência</h2>
              <p>Personalize a aparência da aplicação</p>
            </div>

            <div className="setting-item">
              <div className="setting-info">
                <div className="setting-label">
                  <h3>Modo Escuro</h3>
                  <p>Altere entre o tema claro e escuro</p>
                </div>
                <ToggleIcon isDark={isDarkMode} />
              </div>
              
              <div className="setting-control">
                <button 
                  className={`theme-toggle ${isDarkMode ? 'dark' : 'light'}`}
                  onClick={toggleDarkMode}
                  aria-label={`Alternar para ${isDarkMode ? 'modo claro' : 'modo escuro'}`}
                >
                  <div className="toggle-track">
                    <div className="toggle-thumb"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Seção de Notificações (placeholder para futuras funcionalidades) */}
          <div className="settings-section">
            <div className="section-header">
              <h2>Notificações</h2>
              <p>Configure como você deseja receber notificações</p>
            </div>

            <div className="setting-item disabled">
              <div className="setting-info">
                <div className="setting-label">
                  <h3>Notificações por Email</h3>
                  <p>Receba atualizações importantes por email</p>
                </div>
              </div>
              
              <div className="setting-control">
                <button className="theme-toggle disabled">
                  <div className="toggle-track">
                    <div className="toggle-thumb"></div>
                  </div>
                </button>
              </div>
            </div>

            <div className="setting-item disabled">
              <div className="setting-info">
                <div className="setting-label">
                  <h3>Notificações Push</h3>
                  <p>Receba notificações no navegador</p>
                </div>
              </div>
              
              <div className="setting-control">
                <button className="theme-toggle disabled">
                  <div className="toggle-track">
                    <div className="toggle-thumb"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Seção de Privacidade (placeholder para futuras funcionalidades) */}
          <div className="settings-section">
            <div className="section-header">
              <h2>Privacidade e Segurança</h2>
              <p>Gerencie suas configurações de privacidade</p>
            </div>

            <div className="setting-item disabled">
              <div className="setting-info">
                <div className="setting-label">
                  <h3>Coleta de Dados de Uso</h3>
                  <p>Ajude-nos a melhorar a plataforma compartilhando dados de uso</p>
                </div>
              </div>
              
              <div className="setting-control">
                <button className="theme-toggle disabled">
                  <div className="toggle-track">
                    <div className="toggle-thumb"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="settings-footer">
          <p>
            <strong>Versão:</strong> 1.0.0 | 
            <strong> Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Settings; 