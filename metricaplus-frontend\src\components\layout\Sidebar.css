.sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /*background: linear-gradient(180deg, var(--primary-dark) 0%, var(--primary-color) 100%);*/
  background: #f8f9fa;
  color: #333;
  transition: all 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  overflow-x: hidden;
}

/* Dark mode para sidebar */
.dark-theme .sidebar {
  background: #23272f;
  color: #fff;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

.sidebar.expanded {
  width: 240px;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.logo-text {
  color: #333;
  font-weight: 600;
  font-size: 1.2rem;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Dark mode styles */
.dark-theme .logo-text {
  color: #fff;
}

.toggle-button {
  background: none;
  border: none;
  color: rgba(51, 51, 51, 0.7);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.toggle-button:hover {
  background-color: rgba(51, 51, 51, 0.1);
  color: #333;
}

/* Dark mode styles */
.dark-theme .toggle-button {
  color: rgba(255, 255, 255, 0.7);
}

.dark-theme .toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.menu-items {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  flex-grow: 1;
  overflow-y: auto;
}

.menu-item {
  margin-bottom: 0.5rem;
  position: relative;
  transition: background-color 0.2s ease;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.8rem 1rem;
  text-decoration: none;
  color: rgba(51, 51, 51, 0.7);
  transition: color 0.2s ease, background-color 0.2s ease;
  white-space: nowrap;
  border-radius: 6px;
  margin: 0 0.5rem;
}

.menu-item.active .menu-link {
  background-color: rgba(51, 51, 51, 0.1);
  color: #333;
  position: relative;
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 60%;
  width: 4px;
  background-color: #333;
  border-radius: 0 4px 4px 0;
}

.menu-item:hover .menu-link {
  background-color: rgba(51, 51, 51, 0.05);
  color: #333;
}

/* Dark mode styles */
.dark-theme .menu-link {
  color: rgba(255, 255, 255, 0.7);
}

.dark-theme .menu-item.active .menu-link {
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
}

.dark-theme .menu-item.active::before {
  background-color: #fff;
}

.dark-theme .menu-item:hover .menu-link {
  background-color: rgba(255, 255, 255, 0.08);
  color: #fff;
}

.icon {
  min-width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-name {
  margin-left: 12px;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(51, 51, 51, 0.1);
  font-size: 0.8rem;
  color: rgba(51, 51, 51, 0.5);
  display: flex;
  justify-content: center;
}

/* Dark mode styles */
.dark-theme .sidebar-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
}

.sidebar-info, .sidebar-info-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animações e efeitos */
.sidebar-header, 
.menu-item, 
.sidebar-footer {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
}

.sidebar-header {
  animation-delay: 0.1s;
}

.menu-item:nth-child(1) {
  animation-delay: 0.2s;
}

.menu-item:nth-child(2) {
  animation-delay: 0.3s;
}

.menu-item:nth-child(3) {
  animation-delay: 0.4s;
}

.menu-item:nth-child(4) {
  animation-delay: 0.5s;
}

.menu-item:nth-child(5) {
  animation-delay: 0.6s;
}

.menu-item:nth-child(6) {
  animation-delay: 0.7s;
}

.sidebar-footer {
  animation-delay: 0.8s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .sidebar.expanded {
    width: 240px;
  }
  
  .sidebar.collapsed {
    width: 0;
    padding: 0;
  }
  
  .sidebar.collapsed .sidebar-header,
  .sidebar.collapsed .menu-items,
  .sidebar.collapsed .sidebar-footer {
    display: none;
  }
}

/* Scrollbar personalizada */
.menu-items::-webkit-scrollbar {
  width: 5px;
}

.menu-items::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.menu-items::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.menu-items::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
} 