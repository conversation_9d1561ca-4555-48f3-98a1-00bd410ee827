/* Reset CSS e estilos base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Cores do tema claro */
  --primary-color: #00A651;  /* Verde Métrica+ */
  --primary-dark: #008541;   /* Verde mais escuro */
  --primary-light: #33B973;  /* Verde mais claro */
  --accent-color: #F7941D;   /* Laranja Métrica+ */
  --accent-dark: #D67B0A;    /* Laranja mais escuro */
  --accent-light: #FFA940;   /* Laranja mais claro */
  --text-primary: #000000;   /* Preto da logo */
  --text-secondary: #4A4A4A;
  --text-light: #757575;
  --background-color: #f5f7fa;
  --card-color: #ffffff;
  --border-color: #e0e0e0;
  --success-color: var(--primary-color);
  --error-color: #f44336;
  --warning-color: var(--accent-color);
  --info-color: #2196f3;
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Tema escuro */
.dark-theme {
  /* Cores do tema escuro */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-light: #9a9a9a;
  --background-color: #121212;
  --card-color: #1e1e1e;
  --border-color: #333333;
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
  
  /* Cores primárias mantidas mas com ajustes para melhor contraste */
  --primary-color: #00C962;  /* Verde um pouco mais claro para o dark */
  --primary-dark: #00A651;   /* Verde escuro para dark mode */
  --primary-light: #33D485;  /* Verde mais claro */
  --accent-color: #FF9F2B;   /* Laranja mais claro para o dark */
  --accent-light: #FFB752;   /* Laranja mais claro */
  --error-dark: #e53e3e;     /* Vermelho escuro para dark mode */
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

button {
  cursor: pointer;
  font-family: var(--font-family);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.3;
}

p {
  margin-bottom: 1rem;
}

input, select, textarea {
  font-family: var(--font-family);
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Estilos para páginas vazias ou em desenvolvimento */
.empty-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  max-width: 800px;
  margin: 0 auto;
}

.empty-page h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.empty-page p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
}

/* Animações globais */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Utilitários */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-error {
  color: var(--error-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-info {
  color: var(--info-color);
}

/* Dark mode para toasts */
.dark-theme .Toastify__toast-container {
  background: transparent;
}

.dark-theme .Toastify__toast {
  background: var(--card-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.dark-theme .Toastify__toast--success {
  background: var(--success-color);
}

.dark-theme .Toastify__toast--error {
  background: var(--error-color);
}

.dark-theme .Toastify__toast--warning {
  background: var(--warning-color);
}

.dark-theme .Toastify__toast--info {
  background: var(--info-color);
}

.dark-theme .Toastify__progress-bar {
  background: var(--primary-color);
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

/* Dark mode para scrollbar */
.dark-theme ::-webkit-scrollbar-track {
  background: var(--card-color);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Media queries para responsividade */
@media (max-width: 1200px) {
  :root {
    font-size: 15px;
  }
}

@media (max-width: 992px) {
  :root {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  :root {
    font-size: 13px;
  }
  
  .empty-page {
    padding: 2rem 1rem;
  }
}

@media (max-width: 576px) {
  :root {
    font-size: 12px;
  }
}

/* Fonte Inter da Google */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Loading Spinner Estilizado */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 120, 212, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 16px;
  color: var(--primary-color);
  font-weight: 500;
  font-size: 16px;
}

/* Mensagem de Erro Estilizada */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  margin: 2rem auto;
  max-width: 500px;
  background-color: #fff;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message {
  color: var(--error-color);
  font-size: 18px;
  font-weight: 500;
}

/* Estilização da Tela de Login */
.login-page {
  display: flex;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  background: linear-gradient(135deg, #0078d4 0%, #2b88d8 50%, #50e6ff 100%);
  clip-path: polygon(30% 0, 100% 0, 100% 100%, 0% 100%);
  z-index: 0;
}

.login-card {
  position: relative;
  z-index: 1;
  background-color: white;
  max-width: 400px;
  width: 90%;
  margin: auto;
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  text-align: center;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.login-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
  color: white;
}

.logo-text {
  color: white;
  font-size: 1.6rem;
  font-weight: bold;
}

.login-title {
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.login-divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.login-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
}

.login-divider span {
  position: relative;
  padding: 0 15px;
  background-color: white;
  color: var(--text-lighter);
}

.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 0;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 2rem;
}

.login-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
}

.login-icon {
  margin-right: 10px;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  color: white;
}

.login-footer {
  font-size: 0.8rem;
  color: var(--text-lighter);
  margin-top: 2rem;
}

/* Estilos para MashupList */
.mashup-container {
  width: 100%;
  margin: 2rem auto;
  padding: 0 2rem;
}

.mashup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  gap:100px;
}

.header-highlight {
  color: var(--primary-color);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
}

.user-details {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.user-name {
  font-weight: 600;
  color: var(--text-dark);
}

.user-email {
  font-size: 0.8rem;
  color: var(--text-light);
}

.logout-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: transparent;
  color: var(--text-light);
  border: 1px solid lightgray;
  border-radius: var(--radius);
  font-size: 0.9rem;
  transition: var(--transition);
  cursor: pointer;
}

.logout-button:hover {
  background-color: #f3f2f1;
  color: var(--text-dark);
}

.logout-icon {
  margin-right: 8px;
}

/* Grid Layout para os Cards de Mashup */
.mashup-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.mashup-card {
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
  overflow: hidden;
  height: 100%;
  border: 1px solid var(--border-color);
}

.mashup-card:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.mashup-card-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.mashup-title {
  color: var(--primary-color);
  margin-bottom: 0.8rem;
  font-size: 1.3rem;
}

.mashup-description {
  color: var(--text-light);
  /*margin-bottom: 1.5rem;*/
  /*flex-grow: 1;*/
}

.mashup-link-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  padding: 10px 20px;
  border-radius: var(--radius);
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
}

.mashup-link-button:hover {
  background-color: var(--primary-dark);
  color: white;
  font-weight: 700;
}

.arrow-icon {
  margin-left: 8px;
}

.no-mashups-card {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  border: 1px dashed var(--border-color);
}

.no-mashups-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--text-lighter);
}

.no-mashups-text {
  color: var(--text-light);
  font-size: 1.1rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .mashup-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .user-info {
    width: 100%;
    justify-content: space-between;
  }
  
  .login-background {
    width: 100%;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 70%);
  }
}

@media (max-width: 480px) {
  .mashup-grid {
    grid-template-columns: 1fr;
  }
  
  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .login-card {
    padding: 2rem 1rem;
  }
}

/* Estilos para a tela de diagnóstico */
.debug-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.debug-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px 0;
}

.debug-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.debug-section h2 {
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 15px;
  border-left: 4px solid #0056b3;
  padding-left: 10px;
}

.debug-section h3 {
  color: #555;
  font-size: 1.2rem;
  margin: 15px 0 10px;
}

.debug-data {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
  overflow: auto;
}

.debug-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.4;
}

.token-box {
  max-height: 100px;
  overflow-y: auto;
  background-color: #282c34;
  color: #abb2bf;
  border-radius: 4px;
  padding: 10px;
}

.back-link {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
  padding: 6px 12px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.back-link:hover {
  background-color: #e0e0e0;
}

.debug-link {
  display: inline-flex;
  align-items: center;
  margin-right: 10px;
  padding: 6px 12px;
  background-color: #e8f0fe;
  color: #1a73e8;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.debug-link:hover {
  background-color: #d2e3fc;
}
