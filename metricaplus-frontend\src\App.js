import React from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';

// Contexts
import { ThemeProvider } from './contexts/ThemeContext';

// Layouts
import MainLayout from './components/layout/MainLayout';

// Pages
import Login from './components/pages/Login';
import Dashboard from './components/pages/Dashboard';
import MashupList from './components/pages/MashupList';
import AuthDebug from './components/pages/AuthDebug';
import Users from './components/pages/Users';
import Settings from './components/pages/Settings';

// Componentes
import LoadingSpinner from './components/common/LoadingSpinner';

// Componente de proteção de rota
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth0();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return children;
};

function App() {
  const { isLoading } = useAuth0();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <ThemeProvider>
      <div className="App">
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
        <Routes>
        <Route path="/login" element={<Login />} />
        
        {/* Rotas protegidas com layout principal */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <MainLayout>
                <Dashboard />
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/portal"
          element={
            <ProtectedRoute>
              <MainLayout>
                <MashupList />
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/debug"
          element={
            <ProtectedRoute>
              <MainLayout>
              <AuthDebug />
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        {/* Rotas adicionais para novas páginas */}
        <Route
          path="/users"
          element={
            <ProtectedRoute>
              <MainLayout>
                <Users />
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/analytics"
          element={
            <ProtectedRoute>
              <MainLayout>
                <div className="empty-page">
                  <h2>Página de Analytics</h2>
                  <p>Esta página está em desenvolvimento.</p>
                </div>
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/reports"
          element={
            <ProtectedRoute>
              <MainLayout>
                <div className="empty-page">
                  <h2>Página de Relatórios</h2>
                  <p>Esta página está em desenvolvimento.</p>
                </div>
              </MainLayout>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/settings"
          element={
            <ProtectedRoute>
              <MainLayout>
                <Settings />
              </MainLayout>
            </ProtectedRoute>
          }
        />
        </Routes>
      </div>
    </ThemeProvider>
  );
}

export default App;
