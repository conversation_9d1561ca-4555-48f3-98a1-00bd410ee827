import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import LogoMetricaPlus from '../../assets/img/logoMetricaPlus_simbol.png';
import './Sidebar.css';

// Ícones
const DashboardIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="3" y="3" width="7" height="9" />
    <rect x="14" y="3" width="7" height="5" />
    <rect x="14" y="12" width="7" height="9" />
    <rect x="3" y="16" width="7" height="5" />
  </svg>
);

const UsersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
    <circle cx="9" cy="7" r="4" />
    <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
  </svg>
);

const AnalyticsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M3 3v18h18" />
    <path d="M18 17V9" />
    <path d="M13 17V5" />
    <path d="M8 17v-3" />
  </svg>
);

const ReportsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
    <polyline points="14 2 14 8 20 8" />
    <line x1="16" y1="13" x2="8" y2="13" />
    <line x1="16" y1="17" x2="8" y2="17" />
    <polyline points="10 9 9 9 8 9" />
  </svg>
);

const SettingsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="3" />
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
  </svg>
);

const DebugIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M9 9h.01" />
    <path d="M15 9h.01" />
    <path d="M9 15h.01" />
    <path d="M15 15h.01" />
    <path d="M21 11v1a8 8 0 0 1-8 8h-2a8 8 0 0 1-8-8V8a8 8 0 0 1 8-8h2a8 8 0 0 1 8 8v1" />
    <path d="M8 11v1a4 4 0 0 0 4 4h4" />
  </svg>
);

const PortalIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

const LogoIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" />
  </svg>
);

const menuItems = [
  { id: 'home', name: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { id: 'portal', name: 'Portal de Dados', icon: <PortalIcon />, path: '/portal' },
  { id: 'users', name: 'Usuários', icon: <UsersIcon />, path: '/users' },
  { id: 'analytics', name: 'Analytics', icon: <AnalyticsIcon />, path: '/analytics' },
  { id: 'reports', name: 'Relatórios', icon: <ReportsIcon />, path: '/reports' },
  { id: 'debug', name: 'Diagnóstico', icon: <DebugIcon />, path: '/debug' },
  { id: 'settings', name: 'Configurações', icon: <SettingsIcon />, path: '/settings' }
];

const Sidebar = ({ onToggle, expanded, setExpanded, manuallyToggled, setManuallyToggled }) => {
  const [activeItem, setActiveItem] = useState('home');
  const [hasAdminPermission, setHasAdminPermission] = useState(false);
  const location = useLocation();
  const { getAccessTokenSilently } = useAuth0();

  // Verifica se o usuário tem permissão de admin
  useEffect(() => {
    const checkAdminPermission = async () => {
      try {
        const token = await getAccessTokenSilently();
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        setHasAdminPermission(tokenPayload.permissions?.includes('admin:all') || false);
      } catch (error) {
        console.error('Erro ao verificar permissões:', error);
        setHasAdminPermission(false);
      }
    };

    checkAdminPermission();
  }, [getAccessTokenSilently]);

  useEffect(() => {
    // Atualiza o item ativo com base na URL atual
    const currentPath = location.pathname;
    const currentItem = menuItems.find(item => {
      if (item.path === '/' && currentPath === '/') return true;
      if (item.path !== '/' && currentPath.startsWith(item.path)) return true;
      return false;
    });

    if (currentItem) {
      setActiveItem(currentItem.id);
    }
  }, [location]);

  const toggleSidebar = () => {
    const newExpanded = !expanded;
    setExpanded(newExpanded);
    setManuallyToggled(true);
    if (onToggle) {
      onToggle(newExpanded);
    }
  };

  // Verifica tamanho da janela para ajustar sidebar em dispositivos móveis
  useEffect(() => {
    const handleResize = () => {
      if (!manuallyToggled) {
        const newExpanded = window.innerWidth >= 768;
        setExpanded(newExpanded);
        if (onToggle) {
          onToggle(newExpanded);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [manuallyToggled, onToggle]);

  return (
    <aside className={`sidebar ${expanded ? 'expanded' : 'collapsed'}`}>
      <div className="sidebar-header">
        <div className="logo-container">
          {/* <span style={{ fontSize: '2rem', fontWeight: 'bold', color: 'white' }}>+</span> */}
          <img src={LogoMetricaPlus} alt="Logo MetricaPlus" className="logo-icon" width="24" height="24" />
          {expanded && <span className="logo-text">MetricaPlus</span>}
        </div>
        <button className="toggle-button" onClick={toggleSidebar}>
          {expanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="15 18 9 12 15 6" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          )}
        </button>
      </div>

      <ul className="menu-items">
        {menuItems
          .filter(item => item.id !== 'users' || hasAdminPermission)
          .map(item => (
            <li
              key={item.id}
              className={`menu-item ${activeItem === item.id ? 'active' : ''}`}
              onClick={() => setActiveItem(item.id)}
            >
              <Link to={item.path} className="menu-link">
                <span className="icon">{item.icon}</span>
                {expanded && <span className="item-name">{item.name}</span>}
              </Link>
            </li>
          ))}
      </ul>

      <div className="sidebar-footer">
        {expanded ? (
          <div className="sidebar-info">
            <div className="version">v1.0.0</div>
          </div>
        ) : (
          <div className="sidebar-info-collapsed">
            <div className="version-collapsed">1.0</div>
          </div>
        )}
      </div>
    </aside>
  );
};

export default Sidebar; 