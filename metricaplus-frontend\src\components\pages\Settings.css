.settings-container {
  min-height: calc(100vh - 140px);
  padding: 2rem;
  background: var(--background-color);
}

.settings-content {
  max-width: 800px;
  margin: 0 auto;
}

.settings-header {
  margin-bottom: 3rem;
  text-align: center;
}

.settings-header h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.settings-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.settings-section:hover {
  box-shadow: var(--shadow-lg);
}

.section-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.section-header h2 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
  color: white;
}

.section-header p {
  font-size: 0.95rem;
  margin: 0;
  opacity: 0.9;
}

.setting-item {
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color var(--transition-fast);
  position: relative;
}

.setting-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.setting-item:hover:not(.disabled) {
  background-color: var(--background-color);
}

.setting-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.setting-label h3 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
  font-weight: 600;
}

.setting-label p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  transition: all var(--transition-fast);
}

.setting-control {
  display: flex;
  align-items: center;
}

.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50px;
  transition: all var(--transition-fast);
  position: relative;
}

.theme-toggle:hover:not(.disabled) {
  transform: scale(1.05);
}

.theme-toggle.disabled {
  cursor: not-allowed;
}

.toggle-track {
  width: 64px;
  height: 32px;
  background: var(--border-color);
  border-radius: 16px;
  position: relative;
  transition: all var(--transition-normal);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-toggle.light .toggle-track {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.theme-toggle.dark .toggle-track {
  background: linear-gradient(135deg, #4a5568, #2d3748);
}

.toggle-thumb {
  width: 28px;
  height: 28px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle.dark .toggle-thumb {
  transform: translateX(32px);
  background: #1a202c;
  color: white;
}

.theme-toggle.light .toggle-thumb {
  transform: translateX(0);
  background: white;
  color: #ffd700;
}

.settings-footer {
  margin-top: 3rem;
  padding: 2rem;
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.settings-footer p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

.settings-footer strong {
  color: var(--text-primary);
}

/* Animações */
@keyframes toggleSlide {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(32px);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .settings-container {
    padding: 1rem;
  }

  .settings-header h1 {
    font-size: 2rem;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .setting-info {
    width: 100%;
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }

  .section-header {
    padding: 1rem 1.5rem;
  }

  .setting-item {
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .settings-header h1 {
    font-size: 1.8rem;
  }

  .section-header h2 {
    font-size: 1.3rem;
  }

  .setting-label h3 {
    font-size: 1rem;
  }
} 