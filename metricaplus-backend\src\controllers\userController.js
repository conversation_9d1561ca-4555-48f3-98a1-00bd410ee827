const { auth0Management } = require('../config/auth0');
const { logInfo, logWarning, logError, logSuccess } = require('../utils/logger');

// Listar todos os usuários
exports.listarUsuarios = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { q, page = 0, per_page = 10, sort } = req.query;

    logInfo(`[${requestId}] Buscando usuários com parâmetros:`, { q, page, per_page, sort });

    const params = {
      page,
      per_page,
      sort,
      include_totals: true,
    };

    // Se houver um termo de busca, cria uma query avançada para buscar por nome ou email
    if (q) {
      params.q = `name:*${q}* OR email:*${q}*`;
      params.search_engine = 'v3';
    }

    const response = await auth0Management.users.getAll(params);
    const { users, total } = response.data;
    logSuccess(`[${requestId}] ${users.length} usuários encontrados (Total: ${total})`);
    res.json({ users, total });
  } catch (error) {
    logError(`[${requestId}] Erro ao buscar usuários:`, error);
    res.status(500).json({ error: 'Erro ao buscar usuários' });
  }
};

// Buscar usuário por ID
exports.buscarUsuarioPorId = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    logInfo(`[${requestId}] Buscando usuário com ID: ${id}`);

    const user = await auth0Management.users.get({ id });
    logSuccess(`[${requestId}] Usuário encontrado: ${user.email}`);
    res.json(user);
  } catch (error) {
    logError(`[${requestId}] Erro ao buscar usuário:`, error);
    res.status(500).json({ error: 'Erro ao buscar usuário' });
  }
};

// Criar novo usuário
exports.criarUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { email, password, name } = req.body;
    logInfo(`[${requestId}] Criando novo usuário: ${email}`);

    const user = await auth0Management.users.create({
      email,
      password,
      name,
      connection: 'Username-Password-Authentication'
    });

    logSuccess(`[${requestId}] Usuário criado com sucesso: ${email}`);
    return res.status(201).json(user);

  } catch (error) {
    const errMsg = error.error?.message || error.message || '';

    // Senha fraca
    if (errMsg.includes('PasswordStrengthError') || errMsg.includes('too weak')) {
      logError(`[${requestId}] Senha fraca detectada:`, errMsg);
      return res
        .status(400)
        .json({
          error: 'A senha é muito fraca. Use ao menos 8 caracteres, incluindo letras, números e caracteres especiais.'
        });
    }

    // Usuário já existe
    if (errMsg.includes('already exists')) {
      logError(`[${requestId}] Tentativa de criar usuário existente:`, errMsg);
      return res
        .status(409)
        .json({
          error: 'Este e-mail já está cadastrado. Use outro ou recupere sua senha.'
        });
    }

    // Erro genérico
    logError(`[${requestId}] Erro ao criar usuário:`, error);
    return res.status(500).json({ error: 'Erro interno ao criar usuário' });
  }
};

// Atualizar usuário
exports.atualizarUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    const updates = req.body;
    logInfo(`[${requestId}] Atualizando usuário ${id} com dados:`, updates);

    const user = await auth0Management.users.update({ id }, updates);
    if (!user.email && updates.email) {
      user.email = updates.email; // Garantir que temos o email na resposta
    }
    logSuccess(`[${requestId}] Usuário atualizado com sucesso: ${user.email || id}`);
    res.json(user);
  } catch (error) {
    const errMsg = error.error?.message || error.message || '';
    // caso seja o erro de atributos não-editáveis em conexões sociais
    if (errMsg.includes('cannot be updated') && errMsg.includes('google-oauth2')) {
      logError(`[${requestId}] Tentativa de editar usuário social:`, errMsg);
      return res
        .status(400)
        .json({
          error: 'Este usuário foi criado via Google e não permite alteração de nome/email.'
        });
    }

    // caso genérico
    logError(`[${requestId}] Erro ao atualizar usuário:`, error);
    return res.status(500).json({ error: 'Erro ao atualizar usuário' });
  }
};

// Deletar usuário
exports.deletarUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    logInfo(`[${requestId}] Deletando usuário: ${id}`);

    await auth0Management.users.delete({ id });
    logSuccess(`[${requestId}] Usuário deletado com sucesso: ${id}`);
    res.status(204).send();
  } catch (error) {
    logError(`[${requestId}] Erro ao deletar usuário:`, error);
    res.status(500).json({ error: 'Erro ao deletar usuário' });
  }
};

// Obter permissões de um usuário
exports.obterPermissoesUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    logInfo(`[${requestId}] Obtendo permissões do usuário: ${id}`);

    // Método mais seguro baseado na biblioteca auth0-js que é mais estável
    const axios = require('axios');
    const domain = process.env.AUTH0_DOMAIN;
    const clientId = process.env.AUTH0_MANAGEMENT_CLIENT_ID;
    const clientSecret = process.env.AUTH0_MANAGEMENT_CLIENT_SECRET;
    const audience = `https://${domain}/api/v2/`;

    // Primeiro, vamos tentar obter um token da API Management do Auth0
    const tokenResponse = await axios.post(`https://${domain}/oauth/token`, {
      client_id: clientId,
      client_secret: clientSecret,
      audience: audience,
      grant_type: 'client_credentials'
    });

    const accessToken = tokenResponse.data.access_token;

    // Agora fazemos a chamada para obter as permissões
    const response = await axios({
      method: 'GET',
      url: `https://${domain}/api/v2/users/${id}/permissions`,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    const permissions = response.data;
    logSuccess(`[${requestId}] Permissões obtidas com sucesso para usuário: ${id}`);
    res.json({ data: permissions });
  } catch (error) {
    logError(`[${requestId}] Erro ao obter permissões do usuário:`, error);
    res.status(500).json({ error: 'Erro ao obter permissões do usuário' });
  }
};

// Adicionar permissões a um usuário
exports.adicionarPermissoesUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    const { permissions } = req.body;
    
    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      logWarning(`[${requestId}] Tentativa de adicionar permissões inválidas`);
      return res.status(400).json({ error: 'Permissões inválidas fornecidas' });
    }
    
    logInfo(`[${requestId}] Adicionando permissões ao usuário: ${id}`);

    // Método mais seguro baseado na biblioteca auth0-js que é mais estável
    const axios = require('axios');
    const domain = process.env.AUTH0_DOMAIN;
    const clientId = process.env.AUTH0_MANAGEMENT_CLIENT_ID;
    const clientSecret = process.env.AUTH0_MANAGEMENT_CLIENT_SECRET;
    const audience = `https://${domain}/api/v2/`;

    // Primeiro, vamos tentar obter um token da API Management do Auth0
    const tokenResponse = await axios.post(`https://${domain}/oauth/token`, {
      client_id: clientId,
      client_secret: clientSecret,
      audience: audience,
      grant_type: 'client_credentials'
    });

    const accessToken = tokenResponse.data.access_token;

    // Agora fazemos a chamada para adicionar as permissões
    await axios({
      method: 'POST',
      url: `https://${domain}/api/v2/users/${id}/permissions`,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      data: { permissions }
    });

    logSuccess(`[${requestId}] Permissões adicionadas com sucesso ao usuário: ${id}`);
    res.status(201).json({ success: true });
  } catch (error) {
    logError(`[${requestId}] Erro ao adicionar permissões ao usuário:`, error);
    res.status(500).json({ error: 'Erro ao adicionar permissões ao usuário' });
  }
};

// Remover permissões de um usuário
exports.removerPermissoesUsuario = async (req, res) => {
  const requestId = req.requestId;
  try {
    const { id } = req.params;
    const { permissions } = req.body;
    
    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      logWarning(`[${requestId}] Tentativa de remover permissões inválidas`);
      return res.status(400).json({ error: 'Permissões inválidas fornecidas' });
    }
    
    logInfo(`[${requestId}] Removendo permissões do usuário: ${id}`);

    // Método mais seguro baseado na biblioteca auth0-js que é mais estável
    const axios = require('axios');
    const domain = process.env.AUTH0_DOMAIN;
    const clientId = process.env.AUTH0_MANAGEMENT_CLIENT_ID;
    const clientSecret = process.env.AUTH0_MANAGEMENT_CLIENT_SECRET;
    const audience = `https://${domain}/api/v2/`;

    // Primeiro, vamos tentar obter um token da API Management do Auth0
    const tokenResponse = await axios.post(`https://${domain}/oauth/token`, {
      client_id: clientId,
      client_secret: clientSecret,
      audience: audience,
      grant_type: 'client_credentials'
    });

    const accessToken = tokenResponse.data.access_token;

    // Agora fazemos a chamada para remover as permissões
    await axios({
      method: 'DELETE',
      url: `https://${domain}/api/v2/users/${id}/permissions`,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      data: { permissions }
    });

    logSuccess(`[${requestId}] Permissões removidas com sucesso do usuário: ${id}`);
    res.status(200).json({ success: true });
  } catch (error) {
    logError(`[${requestId}] Erro ao remover permissões do usuário:`, error);
    res.status(500).json({ error: 'Erro ao remover permissões do usuário' });
  }
};

// Listar APIs (Resource Servers) disponíveis
exports.listarAPIs = async (req, res) => {
  const requestId = req.requestId;
  try {
    logInfo(`[${requestId}] Listando APIs (Resource Servers) disponíveis`);
    
    const apiList = await auth0Management.resourceServers.getAll();
    logSuccess(`[${requestId}] Lista de APIs obtida com sucesso: ${apiList.length} APIs encontradas`);
    
    res.json(apiList);
  } catch (error) {
    logError(`[${requestId}] Erro ao listar APIs:`, error);
    res.status(500).json({ error: 'Erro ao listar APIs disponíveis' });
  }
}; 