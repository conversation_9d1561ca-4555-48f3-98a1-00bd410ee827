.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background-color: var(--background-color);
  position: relative;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: all 0.3s ease;
}

.main-content.sidebar-expanded {
  margin-left: 240px;
}

.main-content.sidebar-collapsed {
  margin-left: 70px;
}

.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-x: hidden;
}

.app-footer {
  padding: 1rem 1.5rem;
  background-color: var(--card-color);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.footer-copyright {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-link {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-decoration: none;
  position: relative;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--primary-color);
}

.footer-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.footer-link:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Animações e efeitos */
.content-area {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.2s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .main-content.sidebar-expanded {
    margin-left: 240px;
  }
  
  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
  
  .content-area {
    padding: 1rem;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }
} 