# MetricaPlus - Aplicação com RBAC

Esta aplicação fornece uma interface para acessar mashups do Qlik com autenticação Auth0 e controle de acesso baseado em papéis (RBAC).

## Estrutura do Projeto

O projeto está dividido em duas partes principais:

1. **Frontend (React)** - Interface de usuário para login e acesso aos mashups
2. **Backend (Node.js)** - API para verificação de permissões e listagem de mashups disponíveis

## Configuração do Auth0

Para configurar o Auth0 corretamente, siga os passos abaixo:

1. Crie uma conta no [Auth0](https://auth0.com/)
2. Crie uma aplicação Single Page Application (SPA)
3. Configure as URIs de callback e logout para apontar para seu frontend
4. Crie uma API no Auth0 com o identificador `https://metricaplus.us.qlikcloud.com/`
5. Configure as permissões da API (por exemplo, `read:mashups`, `access:https://metricaplus.us.qlikcloud.com/`, `admin:all`)
6. <PERSON><PERSON> papé<PERSON> (roles) e atribua as permissões adequadas
7. Atribua papéis aos usuários

### Configuração RBAC no Auth0

1. No painel do Auth0, vá para "APIs" e selecione sua API
2. Ative a opção "Enable RBAC" 
3. Ative a opção "Add Permissions in the Access Token"
4. Configure as permissões necessárias na seção "Permissions"

## Instalação e Execução

### Frontend

```bash
cd metricaplus-frontend
npm install
# Edite o arquivo .env com as configurações do Auth0
npm start
```

### Backend

```bash
cd metricaplus-backend
npm install
# Edite o arquivo .env com as configurações do Auth0
npm run dev
```

## Fluxo de Funcionamento

1. O usuário acessa a aplicação e é redirecionado para a tela de login do Auth0
2. Após autenticação bem-sucedida, o usuário recebe um token JWT com suas permissões
3. O frontend faz uma requisição ao backend com o token JWT
4. O backend verifica as permissões do usuário e retorna a lista de mashups disponíveis
5. O usuário pode acessar os mashups permitidos pelo seu papel

## Personalização

Para adicionar novos mashups ou endpoints, edite o objeto `allowedEndpoints` no arquivo `server.js` do backend. 