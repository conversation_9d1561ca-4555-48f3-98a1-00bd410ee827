.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  background: var(--card-color);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 99;
  height: 70px;
  transition: all 0.3s ease;
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  position: relative;
  overflow: hidden;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #3f51b5, #7986cb);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Estilos de pesquisa */
.header-search {
  position: relative;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  border-radius: 20px;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
  width: 200px;
}

.search-container:focus-within {
  background-color: var(--card-color);
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
  width: 250px;
}

.search-icon {
  color: #888;
  margin-right: 8px;
}

.search-input {
  border: none;
  background: none;
  outline: none;
  padding: 0;
  font-size: 0.9rem;
  width: 100%;
  color: var(--text-primary);
  padding-left: 25px;
}

.search-input::placeholder {
  color: #aaa;
}

/* Estilos de notificações */
.header-notifications {
  position: relative;
}

.notification-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0.5rem;
  color: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.notification-button:hover {
  background-color: var(--background-color);
  color: var(--primary-color);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #f44336;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(40%, -40%);
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(40%, -40%) scale(1);
  }
  50% {
    transform: translate(40%, -40%) scale(1.2);
  }
  100% {
    transform: translate(40%, -40%) scale(1);
  }
}

.notifications-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  width: 350px;
  background: var(--card-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  animation: fadeInDown 0.3s ease;
  max-height: 450px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.notifications-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mark-all-read {
  background: none;
  border: none;
  font-size: 0.8rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.mark-all-read:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.notifications-list {
  overflow-y: auto;
  max-height: 300px;
  padding: 0.5rem 0;
}

.notifications-list::-webkit-scrollbar {
  width: 4px;
}

.notifications-list::-webkit-scrollbar-track {
  background: var(--background-color);
}

.notifications-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

.notification-item {
  display: flex;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background-color: var(--background-color);
}

.notification-item.unread {
  background-color: rgba(0, 166, 81, 0.1);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color);
  border-radius: 0 3px 3px 0;
}

.notification-icon-container {
  display: flex;
  align-items: flex-start;
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-icon {
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon.info {
  color: var(--info-color);
  background-color: rgba(33, 150, 243, 0.1);
}

.notification-icon.success {
  color: var(--success-color);
  background-color: rgba(76, 175, 80, 0.1);
}

.notification-icon.warning {
  color: var(--warning-color);
  background-color: rgba(255, 152, 0, 0.1);
}

.notification-content {
  flex-grow: 1;
}

.notification-text {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--text-light);
  display: block;
}

.no-notifications {
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.notifications-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Estilos do perfil */
.header-profile {
  position: relative;
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  transition: background-color 0.2s ease;
}

.profile-button:hover {
  background-color: var(--background-color);
}

.profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.profile-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-right: 0.25rem;
}

.dropdown-arrow {
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.profile-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  width: 280px;
  background: var(--card-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  animation: fadeInDown 0.3s ease;
  overflow: hidden;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
}

.dropdown-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  border: 3px solid #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dropdown-user-info {
  flex-grow: 1;
  overflow: hidden;
}

.dropdown-name {
  margin: 0 0 4px 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-email {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-menu {
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
}

.profile-menu-item {
  margin: 0;
}

.menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 0.5rem 0;
}

.profile-menu-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text-secondary);
  transition: background-color 0.2s ease, color 0.2s ease;
  font-size: 0.9rem;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
}

.profile-menu-link:hover {
  background-color: var(--background-color);
  color: var(--primary-color);
}

.profile-menu-link svg {
  color: var(--text-light);
  transition: color 0.2s ease;
  flex-shrink: 0;
}

.profile-menu-link:hover svg {
  color: var(--primary-color);
}

.logout-link {
  color: #f44336;
}

.logout-link svg {
  color: #f44336;
}

.logout-link:hover {
  background-color: rgba(244, 67, 54, 0.05);
  color: #d32f2f;
}

.logout-link:hover svg {
  color: #d32f2f;
}

/* Responsividade */
@media (max-width: 768px) {
  .app-header {
    padding: 0.75rem 1rem;
  }
  
  .search-container, .search-container:focus-within {
    width: 150px;
  }
  
  .profile-name {
    display: none;
  }
  
  .notifications-dropdown, .profile-dropdown {
    width: 300px;
    right: -50px;
  }
  
  .notifications-dropdown::before, .profile-dropdown::before {
    right: 60px;
  }
} 

/* Botão para abrir/fechar o sidebar quando está em mobile */
.toggle-sidebar-button-mobile {
  display: none;
  color: #333;
  font-size: 1.5rem;
  cursor: pointer;
  margin-right: 0.2rem;
}

@media (max-width: 768px) {
  .toggle-sidebar-button-mobile {
    display: block;
  }
  .page-title {
    font-size: 1.2rem;
  }
}