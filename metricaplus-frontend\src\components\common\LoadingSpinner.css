.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  padding: 2rem;
  background-color: var(--background-color);
}

.spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;
}

.spinner:before,
.spinner:after,
.spinner-inner:before,
.spinner-inner:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
}

.spinner:before {
  background-color: var(--primary-color);
  animation-name: moveTop;
}

.spinner:after {
  background-color: var(--primary-light);
  animation-name: moveRight;
}

.spinner-inner:before {
  background-color: var(--accent-color);
  animation-name: moveBottom;
}

.spinner-inner:after {
  background-color: var(--accent-light);
  animation-name: moveLeft;
}

@keyframes moveTop {
  0% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  25% {
    transform: translate(-50%, -100%);
    opacity: 1;
  }
  50%, 100% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
}

@keyframes moveRight {
  0%, 25% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  50% {
    transform: translate(0%, -50%);
    opacity: 1;
  }
  75%, 100% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
}

@keyframes moveBottom {
  0%, 50% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  75% {
    transform: translate(-50%, 0%);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
}

@keyframes moveLeft {
  0%, 75% {
    transform: translate(-50%, -50%);
    opacity: 0;
  }
  100% {
    transform: translate(-100%, -50%);
    opacity: 1;
    opacity: 0;
  }
}

.loading-text {
  font-size: 1rem;
  color: var(--text-secondary);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
} 