.auth-debug-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-header {
  margin-bottom: 2rem;
  text-align: center;
}

.debug-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.debug-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

.debug-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-button:hover:not(.active) {
  color: var(--text-primary);
  background-color: var(--background-color);
}

.tab-button svg {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.tab-button.active svg {
  opacity: 1;
}

.tab-content {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para a aba de usuário */
.user-profile-card {
  background: var(--card-color);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: 2rem;
}

.user-profile-header {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin-right: 1.5rem;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
}

.user-email {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.user-metadata {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metadata-label {
  font-size: 0.85rem;
  color: var(--text-light);
  font-weight: 500;
}

.metadata-value {
  font-size: 1rem;
  color: var(--text-primary);
  word-break: break-word;
}

.user-json {
  padding: 1.5rem;
  background: var(--background-color);
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.json-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.copy-button {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.85rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.user-json pre {
  background: var(--card-color);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: auto;
  font-size: 0.9rem;
  color: var(--text-primary);
  max-height: 300px;
}

/* Estilos para a aba de token */
.token-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.badge-container {
  display: flex;
  gap: 0.75rem;
}

.token-badge {
  background: #e8eaf6;
  color: #3f51b5;
  padding: 0.4rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.token-card {
  background: var(--card-color);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
}

.token-card h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.permissions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.permission-item:hover {
  background: var(--border-color);
}

.permission-icon {
  color: #4caf50;
  font-weight: bold;
}

.permission-text {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.no-permissions {
  color: var(--text-light);
  text-align: center;
  padding: 1rem;
  font-style: italic;
}

.audience-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.audience-item {
  background: var(--background-color);
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  font-size: 0.9rem;
  max-width: 100%;
  word-break: break-all;
}

.token-raw {
  position: relative;
}

.token-raw-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.token-box {
  background: var(--background-color);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: auto;
  max-height: 200px;
}

.token-box pre {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  word-break: break-all;
  white-space: pre-wrap;
}

/* Estilos para a aba de servidor */
.server-debug {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.server-card {
  background: var(--card-color);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
}

.server-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.access-check {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.access-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-color);
  border-radius: 6px;
}

.access-key {
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 0.5rem;
  flex: 1;
}

.access-value {
  font-weight: 500;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
}

.access-value.success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.access-value.error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
}

.permissions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.permission-chip {
  background: rgba(63, 81, 181, 0.1);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.server-raw-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-response {
  background: var(--background-color);
  padding: 1rem;
  border-radius: 8px;
  overflow: auto;
  max-height: 300px;
}

.server-response pre {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  word-break: break-all;
  white-space: pre-wrap;
}

/* Responsividade */
@media (max-width: 768px) {
  .debug-tabs {
    overflow-x: auto;
    padding-bottom: 0.25rem;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    white-space: nowrap;
  }
  
  .user-profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .user-metadata {
    grid-template-columns: 1fr;
  }
  
  .metadata-item {
    padding: 0.5rem 0;
  }
  
  .server-response,
  .token-box,
  .user-json pre {
    max-height: 200px;
  }
} 