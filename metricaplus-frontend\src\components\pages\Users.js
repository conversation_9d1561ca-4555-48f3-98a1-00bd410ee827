import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import { toast } from 'react-toastify';
import LoadingSpinner from '../common/LoadingSpinner';
import './Users.css';

const Users = () => {
  const { getAccessTokenSilently } = useAuth0();
  const [users, setUsers] = useState([]);
  const [pageLoading, setPageLoading] = useState(true);
  const [createLoading, setCreateLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [perPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    name: '',
  });
  const [editingUser, setEditingUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  
  // Estados para gerenciar permissões
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userPermissions, setUserPermissions] = useState([]);
  const [availableAPIs, setAvailableAPIs] = useState([]);
  const [selectedAPI, setSelectedAPI] = useState('');
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [addPermissionLoading, setAddPermissionLoading] = useState(false);
  const [permissionSearchTerm, setPermissionSearchTerm] = useState('');

  // Busca os usuários através do backend
  const fetchUsers = async (token) => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/users`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          page: currentPage,
          per_page: perPage,
          sort: 'name:1',
          q: searchTerm
        }
      });
      console.log("Atualizando usuários:", response.data);
      setUsers(response.data.users || []);
      const total = response.data.total || 0;
      setTotalPages(Math.ceil(total / perPage));
    } catch (error) {
      if (error.response?.status === 403) {
        setError('Você não tem permissão para acessar esta página');
        toast.error('Você não tem permissão para acessar esta página');
      } else {
        console.error('Erro ao buscar usuários:', error);
        setError('Não foi possível carregar os usuários');
        toast.error('Não foi possível carregar os usuários');
      }
    } finally {
      setPageLoading(false);
    }
  };

  // Função para lidar com a mudança no campo de busca
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(0); // Volta para a primeira página ao buscar
  };

  // Debounce para a busca
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const initializeComponent = async () => {
        try {
          const token = await getAccessTokenSilently();
          await fetchUsers(token);
        } catch (error) {
          console.error('Erro na busca:', error);
        }
      };

      if (searchTerm.length > 2 || searchTerm.length === 0) {
        initializeComponent();
      }	
    }, 300); // Aguarda 300ms após a última digitação

    return () => clearTimeout(timeoutId);
  }, [searchTerm, currentPage, getAccessTokenSilently]);

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        const token = await getAccessTokenSilently();
        await fetchUsers(token);
      } catch (error) {
        console.error('Erro na inicialização:', error);
        setError('Erro ao carregar a página');
        toast.error('Erro ao carregar a página');
        setPageLoading(false);
      }
    };

    initializeComponent();
  }, [getAccessTokenSilently, currentPage]);

  // Busca as APIs disponíveis
  const fetchAPIs = async () => {
    try {
      const token = await getAccessTokenSilently();
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/users/apis/list`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      
      console.log("Resposta APIs:", response.data);
      
      // Verificar a estrutura da resposta
      let apiList = [];
      if (Array.isArray(response.data)) {
        apiList = response.data;
      } else if (response.data && Array.isArray(response.data.data)) {
        apiList = response.data.data;
      } else if (response.data && typeof response.data === 'object') {
        // Tentar extrair arrays de qualquer propriedade no objeto
        for (const key in response.data) {
          if (Array.isArray(response.data[key])) {
            apiList = response.data[key];
            break;
          }
        }
      }
      
      console.log("APIs processadas:", apiList);
      setAvailableAPIs(apiList);
      
      // Remover a seleção automática da primeira API
      setSelectedAPI(''); // Deixar vazio para o usuário selecionar manualmente
    } catch (error) {
      console.error('Erro ao buscar APIs:', error);
      toast.error('Erro ao carregar as APIs disponíveis');
      setAvailableAPIs([]);
    }
  };

  // Busca as permissões de um usuário
  const fetchUserPermissions = async (userId) => {
    setPermissionsLoading(true);
    try {
      const token = await getAccessTokenSilently();
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/users/${userId}/permissions`, {
        headers: {
          Authorization: `Bearer ${token}`,
        }
      });
      // Verificar a estrutura da resposta e armazenar corretamente o array de permissões
      console.log('Permissões retornadas:', response.data.data);
      let userPermissionsTemp = response.data.data;
      if (Array.isArray(userPermissionsTemp)) {
        setUserPermissions(userPermissionsTemp);
      } else if (userPermissionsTemp && Array.isArray(userPermissionsTemp.permissions)) {
        setUserPermissions(userPermissionsTemp.permissions);
      } else {
        // Se não conseguir encontrar o array, inicializa como array vazio
        console.warn('Formato de permissões não reconhecido:', response.data);
        setUserPermissions([]);
      }
    } catch (error) {
      console.error('Erro ao buscar permissões do usuário:', error);
      toast.error('Erro ao carregar permissões do usuário');
      setUserPermissions([]);
    } finally {
      setPermissionsLoading(false);
    }
  };

  // Função para filtrar permissões pelo termo de pesquisa
  const filterPermissionsBySearch = (permissions) => {
    if (!permissionSearchTerm) return permissions;
    
    const searchTermLower = permissionSearchTerm.toLowerCase();
    return permissions.filter(
      permission => 
        permission.value.toLowerCase().includes(searchTermLower) || 
        (permission.description && permission.description.toLowerCase().includes(searchTermLower))
    );
  };

  // Encontra todas as permissões disponíveis para a API selecionada e marca as que o usuário já possui
  const getAllPermissionsForAPI = () => {
    const api = availableAPIs?.find(api => api.identifier === selectedAPI);
    if (!api || !api.scopes) return [];
    
    // Garantir que userPermissions é sempre um array antes de usar
    const permissionsArray = Array.isArray(userPermissions) ? userPermissions : [];
    
    // Filtra as permissões para a API selecionada
    const userPermissionsForAPI = permissionsArray
      .filter(p => p.resource_server_identifier === selectedAPI)
      .map(p => p.permission_name);
    
    // Retorna todas as permissões disponíveis com indicação de quais estão ativas
    return api.scopes.map(scope => ({
      value: scope.value,
      description: scope.description,
      isActive: userPermissionsForAPI.includes(scope.value)
    }));
  };

  // Abre o modal de permissões
  const handleOpenPermissionsModal = async (user) => {
    setSelectedUser(user);
    setIsPermissionsModalOpen(true);
    setPermissionSearchTerm(''); // Limpa a pesquisa anterior
    await fetchAPIs();
    await fetchUserPermissions(user.user_id);
  };

  // Função para alternar uma permissão (adicionar ou remover)
  const togglePermission = async (permissionName, currentStatus) => {
    if (!selectedUser || !selectedAPI) return;
    
    // Armazena o estado atual para possível reversão em caso de erro
    const previousPermissions = [...userPermissions];
    
    // Atualiza imediatamente o estado para refletir a alteração na UI
    const updatedPermissions = [...userPermissions];
    
    if (currentStatus) {
      // Remove a permissão do array
      const index = updatedPermissions.findIndex(
        p => p.permission_name === permissionName && p.resource_server_identifier === selectedAPI
      );
      if (index !== -1) {
        updatedPermissions.splice(index, 1);
      }
    } else {
      // Adiciona a permissão ao array
      updatedPermissions.push({
        permission_name: permissionName,
        resource_server_identifier: selectedAPI
      });
    }
    
    // Atualiza o estado imediatamente
    setUserPermissions(updatedPermissions);
    
    // Inicia a requisição ao servidor
    setAddPermissionLoading(true);
    try {
      const token = await getAccessTokenSilently();
      
      if (currentStatus) {
        // Remove a permissão
        await axios.delete(
          `${process.env.REACT_APP_API_URL}/api/users/${selectedUser.user_id}/permissions`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            data: {
              permissions: [
                {
                  resource_server_identifier: selectedAPI,
                  permission_name: permissionName
                }
              ]
            }
          }
        );
        toast.success('Permissão removida com sucesso!');
      } else {
        // Adiciona a permissão
        await axios.post(
          `${process.env.REACT_APP_API_URL}/api/users/${selectedUser.user_id}/permissions`,
          {
            permissions: [
              {
                resource_server_identifier: selectedAPI,
                permission_name: permissionName
              }
            ]
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            }
          }
        );
        toast.success('Permissão adicionada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao alterar permissão:', error);
      toast.error(`Erro ao ${currentStatus ? 'remover' : 'adicionar'} permissão`);
      
      // Reverte a alteração no estado em caso de erro
      setUserPermissions(previousPermissions);
    } finally {
      setAddPermissionLoading(false);
    }
  };

  const handleCreateUser = async (e) => {
    e.preventDefault();
    setCreateLoading(true);
    try {
      const token = await getAccessTokenSilently();
      const { data: newUserCreated } = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/users`,
        { email: newUser.email, password: newUser.password, name: newUser.name },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      // aguarda 1 segundo antes de recarregar
      setTimeout(async () => {
        //const token = await getAccessTokenSilently();
        await fetchUsers(token);
      }, 3000);
      setNewUser({ email: '', password: '', name: '' });
      toast.success('Usuário criado com sucesso!');
    } catch (error) {
      console.error('Erro ao criar usuário:', error);

      const status = error.response?.status;
      const msg = error.response?.data?.error || '';

      if (status === 400 && msg.includes('senha é muito fraca')) {
        toast.error(msg);
      } else if (status === 409 && msg.includes('já está cadastrado')) {
        toast.error('Este e-mail já está em uso. Se for seu, recupere a senha.');
      } else {
        toast.error('Erro ao criar usuário');
      }
    } finally {
      setCreateLoading(false);
    }
  };


  const handleDeleteUser = async (userId) => {
    setUserToDelete(userId);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    setDeleteLoading(true);
    try {
      const token = await getAccessTokenSilently();

      await axios.delete(`${process.env.REACT_APP_API_URL}/api/users/${userToDelete}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Atualiza a lista localmente
      setUsers(prevUsers => prevUsers.filter(user => user.user_id !== userToDelete));

      toast.success('Usuário deletado com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar usuário:', error);
      setError('Erro ao deletar usuário');
      toast.error('Erro ao deletar usuário');
    } finally {
      setDeleteLoading(false);
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    }
  };

  const handleEditClick = (user) => {
    setEditingUser({
      ...user,
      password: '' // Não incluímos a senha atual por segurança
    });
    setIsModalOpen(true);
  };

  const handleUpdateUser = async (e) => {
    e.preventDefault();
    setUpdateLoading(true);
    try {
      const token = await getAccessTokenSilently();

      const updateData = {
        name: editingUser.name,
        email: editingUser.email,
      };

      if (editingUser.password) {
        updateData.password = editingUser.password;
      }

      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/users/${editingUser.user_id}`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      /*
      // Atualiza a lista localmente
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.user_id === editingUser.user_id ? { ...user, ...response.data } : user
        )
      );

      setIsModalOpen(false);
      setEditingUser(null);
      */

      // Recarrega a lista do servidor
      //await fetchUsers(token);
      // aguarda 1 segundo antes de recarregar
      setTimeout(async () => {
        //const token = await getAccessTokenSilently();
        await fetchUsers(token);
      }, 1000);

      setIsModalOpen(false);
      setEditingUser(null);
      toast.success('Usuário atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error);

      if (
        error.response?.status === 400 &&
        error.response.data.error.includes('Google')
      ) {
        toast.error(
          'Não é possível editar esse usuário porque ele foi criado via conta Google.'
        );
      } else {
        toast.error('Erro ao atualizar usuário');
        setError('Erro ao atualizar usuário');
      }


    } finally {
      setUpdateLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  if (pageLoading) return <LoadingSpinner />;
  if (error) return <div className="error-message">{error}</div>;

  return (
    <div className="users-container">
      <div className="users-header">
        <h2>Gerenciamento de Usuários</h2>
      </div>

      <div className="create-user-form">
        <h3>Criar Novo Usuário</h3>
        <form onSubmit={handleCreateUser}>
          <div className="form-group">
            <input
              type="email"
              placeholder="Email"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              required
            />
          </div>
          <div className="form-group">
            <input
              type="password"
              placeholder="Senha"
              value={newUser.password}
              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              required
            />
          </div>
          <div className="form-group">
            <input
              type="text"
              placeholder="Nome"
              value={newUser.name}
              onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
              required
            />
          </div>
          <button type="submit" className="create-button" disabled={createLoading}>
            {createLoading ? 'Criando...' : 'Criar Usuário'}
          </button>
        </form>
      </div>

      <div className="users-list">
        <div className="users-list-header">
          <h3>Usuários Cadastrados</h3>
          <div className="search-container">
            <input
              type="text"
              placeholder="Buscar usuários..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="search-input"
            />
            <svg 
              className="search-icon" 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
        </div>
        <div className="users-grid">
          {users.map((user) => (
            <div key={user.user_id} className="user-card">
              <div className="user-info">
                <img
                  src={user.picture || 'https://cdn.auth0.com/avatars/default.png'}
                  alt={user.name}
                  className="user-avatar"
                  referrerPolicy="no-referrer"
                />
                <div className="user-details">
                  <h4>{user.name}</h4>
                  <p>{user.email}</p>
                  <p className="user-id">ID: {user.user_id}</p>
                </div>
              </div>
              <div className="user-actions">
                <button
                  onClick={() => handleEditClick(user)}
                  className="edit-button"
                  disabled={updateLoading || deleteLoading}
                >
                  Editar
                </button>
                <button
                  onClick={() => handleDeleteUser(user.user_id)}
                  className="delete-button"
                  disabled={updateLoading || deleteLoading}
                >
                  {deleteLoading ? 'Deletando...' : 'Deletar'}
                </button>
                <button
                  onClick={() => handleOpenPermissionsModal(user)}
                  className="permissions-button"
                  disabled={updateLoading || deleteLoading}
                >
                  Permissões
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="pagination">
          <button
            className="pagination-button"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 0}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
            Anterior
          </button>

          <div className="pagination-info">
            Página {currentPage + 1} de {totalPages || 1}
          </div>

          <button
            className="pagination-button"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages - 1}
          >
            Próxima
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      </div>

      {isModalOpen && editingUser && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Editar Usuário</h3>
              <button
                className="close-button"
                onClick={() => {
                  setIsModalOpen(false);
                  setEditingUser(null);
                }}
              >
                ×
              </button>
            </div>
            <form onSubmit={handleUpdateUser}>
              <div className="form-group">
                <input
                  type="email"
                  placeholder="Email"
                  value={editingUser.email}
                  onChange={(e) =>
                    setEditingUser({ ...editingUser, email: e.target.value })
                  }
                  required
                />
              </div>
              <div className="form-group">
                <input
                  type="password"
                  placeholder="Nova Senha (deixe em branco para manter a atual)"
                  value={editingUser.password || ''}
                  onChange={(e) =>
                    setEditingUser({ ...editingUser, password: e.target.value })
                  }
                />
              </div>
              <div className="form-group">
                <input
                  type="text"
                  placeholder="Nome"
                  value={editingUser.name}
                  onChange={(e) =>
                    setEditingUser({ ...editingUser, name: e.target.value })
                  }
                  required
                />
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="cancel-button"
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingUser(null);
                  }}
                  disabled={updateLoading}
                >
                  Cancelar
                </button>
                <button type="submit" className="save-button" disabled={updateLoading}>
                  {updateLoading ? 'Salvando...' : 'Salvar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isDeleteModalOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Confirmar Exclusão</h3>
              <button
                className="close-button"
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setUserToDelete(null);
                }}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <p>Tem certeza que deseja excluir este usuário? Esta ação não pode ser desfeita.</p>
            </div>
            <div className="modal-footer">
              <button
                type="button"
                className="cancel-button"
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setUserToDelete(null);
                }}
                disabled={deleteLoading}
              >
                Cancelar
              </button>
              <button
                type="button"
                className="delete-button"
                onClick={confirmDelete}
                disabled={deleteLoading}
              >
                {deleteLoading ? 'Excluindo...' : 'Excluir'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Permissões */}
      {isPermissionsModalOpen && selectedUser && (
        <div className="modal-overlay">
          <div className="modal-content permissions-modal">
            <div className="modal-header">
              <h3>Permissões do Usuário - {selectedUser.name}</h3>
              <button
                className="close-button"
                onClick={() => {
                  setIsPermissionsModalOpen(false);
                  setSelectedUser(null);
                  setUserPermissions([]);
                  setSelectedAPI(''); // Limpar a API selecionada ao fechar
                }}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="permissions-section">
                <h4>Selecione a API</h4>
                <select 
                  value={selectedAPI} 
                  onChange={(e) => setSelectedAPI(e.target.value)}
                  className="api-select"
                >
                  <option value="">-- Selecione uma API --</option>
                  {Array.isArray(availableAPIs) && availableAPIs.map((api, index) => (
                    <option key={api.id || `api-${index}`} value={api.identifier}>
                      {api.name}
                    </option>
                  ))}
                </select>
              </div>
              
              {selectedAPI ? (
                <div className="permissions-section">
                  <div className="permissions-search-container">
                    <h4>Gerenciar Permissões</h4>
                    <div className="permissions-search-input-container">
                      <input
                        type="text"
                        placeholder="Buscar permissões..."
                        value={permissionSearchTerm}
                        onChange={(e) => setPermissionSearchTerm(e.target.value)}
                        className="permissions-search-input"
                      />
                      <svg 
                        className="permissions-search-icon" 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="14" 
                        height="14" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                      </svg>
                    </div>
                  </div>
                  {permissionsLoading ? (
                    <div className="permissions-loading">Carregando permissões...</div>
                  ) : getAllPermissionsForAPI().length === 0 ? (
                    <div className="no-permissions">Nenhuma permissão disponível para esta API</div>
                  ) : filterPermissionsBySearch(getAllPermissionsForAPI()).length === 0 ? (
                    <div className="no-permissions">Nenhuma permissão encontrada para o termo de busca</div>
                  ) : (
                    <div className="permissions-list-container">
                      <div className="permissions-list">
                        {filterPermissionsBySearch(getAllPermissionsForAPI()).map((permission, index) => (
                          <div key={index} className="permission-checkbox-item">
                            <div className="permission-checkbox-container">
                              <input
                                type="checkbox"
                                id={`permission-${index}`}
                                checked={permission.isActive}
                                onChange={() => togglePermission(permission.value, permission.isActive)}
                                disabled={addPermissionLoading}
                                className="permission-checkbox"
                              />
                              <label htmlFor={`permission-${index}`} className="permission-label">
                                <span className="permission-name">{permission.value}</span>
                                {permission.description && (
                                  <span className="permission-description">{permission.description}</span>
                                )}
                              </label>
                            </div>
                            <div className="permission-status">
                              {permission.isActive ? (
                                <span className="permission-active">Ativa</span>
                              ) : (
                                <span className="permission-inactive">Inativa</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="permissions-section">
                  <h4>Gerenciar Permissões</h4>
                  <div className="no-permissions">Selecione uma API para gerenciar as permissões</div>
                </div>
              )}
            </div>
            
            <div className="modal-footer">
              <button
                type="button"
                className="close-permissions-button"
                onClick={() => {
                  setIsPermissionsModalOpen(false);
                  setSelectedUser(null);
                  setUserPermissions([]);
                  setSelectedAPI(''); // Limpar a API selecionada ao fechar
                }}
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users; 