/* Estilos para o Modal de Login do Qlik Enterprise */
.enterprise-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.enterprise-modal-container {
  background: var(--card-color);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
  position: relative;
}

.enterprise-modal-header {
  text-align: center;
  margin-bottom: 2rem;
}

.enterprise-modal-title {
  color: var(--text-primary);
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.enterprise-modal-subtitle {
  color: var(--text-secondary);
  margin: 0.5rem 0 0;
  font-size: 0.9rem;
}

.enterprise-modal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.enterprise-modal-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.enterprise-modal-input-group label {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 500;
}

.enterprise-modal-input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: var(--background-color);
  color: var(--text-primary);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.enterprise-modal-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.25);
}

.enterprise-modal-input.input-error {
  border-color: var(--error-color);
}

.input-error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.enterprise-modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.enterprise-modal-button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.enterprise-modal-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.cancel-button-login-enterprise {
  background-color: var(--card-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancel-button-login-enterprise:hover:not(:disabled) {
  background-color: var(--background-color);
  color: var(--text-primary);
  border-color: var(--text-light);
}

.submit-button {
  background-color: var(--primary-color);
  color: white;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.enterprise-modal-error {
  background-color: rgba(229, 62, 62, 0.1);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.error-icon {
  font-size: 1.1rem;
}

.spinner-login-enterprise {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin: 0;
  margin-bottom: 0;
  padding: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.enterprise-modal-input:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  opacity: 0.6;
} 