import { useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';

export const useEChartsTheme = () => {
  const { isDarkMode } = useTheme();

  const theme = useMemo(() => {
    if (isDarkMode) {
      return {
        backgroundColor: 'transparent',
        textStyle: {
          color: '#ffffff'
        },
        title: {
          textStyle: {
            color: '#ffffff'
          }
        },
        legend: {
          textStyle: {
            color: '#ffffff'
          }
        },
        tooltip: {
          backgroundColor: '#2a2a2a',
          borderColor: '#444444',
          textStyle: {
            color: '#ffffff'
          }
        },
        categoryAxis: {
          axisLine: {
            lineStyle: {
              color: '#333333'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#333333'
            }
          },
          axisLabel: {
            color: '#b3b3b3'
          },
          splitLine: {
            lineStyle: {
              color: '#333333'
            }
          }
        },
        valueAxis: {
          axisLine: {
            lineStyle: {
              color: '#333333'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#333333'
            }
          },
          axisLabel: {
            color: '#b3b3b3'
          },
          splitLine: {
            lineStyle: {
              color: '#333333'
            }
          }
        },
        grid: {
          borderColor: '#333333'
        }
      };
    } else {
      return {
        backgroundColor: 'transparent',
        textStyle: {
          color: '#333333'
        },
        title: {
          textStyle: {
            color: '#333333'
          }
        },
        legend: {
          textStyle: {
            color: '#333333'
          }
        },
        tooltip: {
          backgroundColor: '#ffffff',
          borderColor: '#cccccc',
          textStyle: {
            color: '#333333'
          }
        },
        categoryAxis: {
          axisLine: {
            lineStyle: {
              color: '#cccccc'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#cccccc'
            }
          },
          axisLabel: {
            color: '#666666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        valueAxis: {
          axisLine: {
            lineStyle: {
              color: '#cccccc'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#cccccc'
            }
          },
          axisLabel: {
            color: '#666666'
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        grid: {
          borderColor: '#cccccc'
        }
      };
    }
  }, [isDarkMode]);

  return { theme, isDarkMode };
}; 