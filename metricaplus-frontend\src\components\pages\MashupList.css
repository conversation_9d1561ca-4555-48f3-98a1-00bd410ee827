/* Estilos do container principal */
.mashup-container {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Header com controles */
.mashup-header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

/* Estilização da busca */
.mashup-search {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9e9e9e;
}

.mashup-search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: var(--card-color);
  color: var(--text-primary);
}

.mashup-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.2);
}

/* Filtros */
.mashup-filters {
  display: flex;
  gap: 1rem;
}

.mashup-filter-select,
.mashup-sort-select {
  padding: 10px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--card-color);
  font-size: 0.9rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 150px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23555555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.mashup-filter-select:focus,
.mashup-sort-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.2);
}

/* Cards de estatísticas */
.mashup-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-card {
  background: var(--card-color);
  border-radius: 10px;
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Estilos do grid de mashups */
.mashup-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Estilização dos cards */
.mashup-card {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
  overflow: hidden;
  isolation: isolate;
  min-height: 260px; /* Altura mínima fixa para evitar redimensionamento */
  background-color: transparent; /* Removemos o background direto do card */
}

/* Pseudo-elemento para o background com efeito de zoom */
.mashup-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--card-color);
  background-image: var(--card-bg-image);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
  transition: transform 0.4s ease;
  will-change: transform;
}

.mashup-card:hover::before {
  transform: scale(1.08); /* Efeito de zoom apenas no background */
}

.mashup-card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.mashup-card-content {
  position: relative;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0));
  /*backdrop-filter: blur(2px);*/ /* Desfoque leve para melhor legibilidade */
  transition: transform 0.3s ease;
}

.mashup-card-content p,
.mashup-card-content h3 {
  color: white;
}

.mashup-card:hover .mashup-card-content {
  /* transform: translateY(-25px); Removido para que o botão não se mova */
}

.mashup-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.25rem;
}

.mashup-icon {
  width: 50px;
  height: 50px;
  padding: 0.75rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
}

.qlik-hub {
  color: #ffffff;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.qlik-mashup {
  color: #ffffff;
  background: linear-gradient(135deg, gray 0%, lightgray 100%);
}

.cloud-mashup {
  color: #ffffff;
  background: linear-gradient(135deg, gray 0%, lightgray 100%);
}

.mashup-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0 0;
  color: var(--text-primary);
  white-space: nowrap;
  /* impede quebra de linha */
  overflow: hidden;
  /* oculta o excesso */
  text-overflow: ellipsis;
  /* adiciona os "..." */
  max-width: 100%;
  /* respeita a largura da div pai */
  display: block;
}

.mashup-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0 0 0 0;
  /*flex-grow: 1;*/
}

.mashup-text-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* ou ajuste o espaçamento entre os textos */
  margin-bottom: 1rem; /* separa do botão */
}

.mashup-link-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--accent-color);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  margin-top: auto;
  position: relative;
  z-index: 1;
}

.mashup-link-button:hover {
  background-color: var(--accent-dark);
  color: white;
  font-weight: 700;
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.mashup-link-button:hover .arrow-icon {
  transform: translateX(3px);
}

/* State de "Nenhum mashup" */
.no-mashups-card {
  grid-column: 1 / -1;
  background: var(--card-color);
  border-radius: 12px;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.no-mashups-icon {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.no-mashups-text {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

.clear-search-button {
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-search-button:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
  border-color: var(--primary-color);
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(63, 81, 181, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Error container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: var(--card-color);
  border-radius: 10px;
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  margin: 2rem auto;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--error-color);
}

.error-message {
  font-size: 1.1rem;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.5;
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mashup-stats .stat-card:nth-child(1) {
  animation-delay: 0.1s;
}

.mashup-stats .stat-card:nth-child(2) {
  animation-delay: 0.2s;
}

.mashup-stats .stat-card:nth-child(3) {
  animation-delay: 0.3s;
}

.mashup-grid .mashup-card {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

@media (max-width: 768px) {
  .mashup-header-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .mashup-search {
    max-width: 100%;
  }

  .mashup-filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .mashup-stats {
    flex-direction: column;
  }
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  max-width: 180px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

.refresh-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.refresh-button svg {
  width: 20px;
  height: 20px;
}

.refresh-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.refresh-button.loading svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Estilo para o span da URL */
.mashup-url {
  font-size: 0.8rem;
  color: white;
  margin-top: 0;
  margin-bottom: 0;
  word-break: break-all;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s ease, opacity 0.4s ease;
  font-weight: 500;
  z-index: 2; /* Garantir que aparece acima de outros elementos */
}

.mashup-card:hover .mashup-url {
  max-height: 60px;
  opacity: 1;
}

.mashup-icon-container {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease, height 0.3s ease;
  height: 58px;
}

.mashup-card:hover .mashup-icon-container {
  opacity: 0;
  --transform: translateY(-10px);
  pointer-events: none; /* evita cliques enquanto invisível */
  height: 0;
  margin-bottom: 0;
}