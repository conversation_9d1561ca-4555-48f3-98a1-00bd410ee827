const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { checkJwt, checkAdminPermission } = require('../middleware/auth');

// Aplicar middlewares em todas as rotas
router.use(checkJwt);
router.use(checkAdminPermission);

// Rotas de usuários
router.get('/', userController.listarUsuarios);
router.get('/:id', userController.buscarUsuarioPorId);
router.post('/', userController.criarUsuario);
router.put('/:id', userController.atualizarUsuario);
router.delete('/:id', userController.deletarUsuario);

// Rotas de permissões de usuários
router.get('/:id/permissions', userController.obterPermissoesUsuario);
router.post('/:id/permissions', userController.adicionarPermissoesUsuario);
router.delete('/:id/permissions', userController.removerPermissoesUsuario);

// Rota para listar APIs disponíveis
router.get('/apis/list', userController.listarAPIs);

module.exports = router; 