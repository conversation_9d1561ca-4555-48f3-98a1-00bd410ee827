require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { expressjwt: jwt } = require('express-jwt');
const jwksRsa = require('jwks-rsa');
const jwtAuthz = require('express-jwt-authz');
const path = require('path');
const fs = require('fs');
const https = require('https');
const axios = require('axios');
const { ManagementClient } = require('auth0');
const { logInfo, logWarning, logError, logSuccess, logRequest, logErrorHandler } = require('./src/utils/logger');
const app = express();
const userRoutes = require('./src/routes/userRoutes');

// Middlewares
app.use(cors());
app.use(express.json());
app.use(logRequest); // Adiciona logging de requisições

const tough = require('tough-cookie');
const cheerio = require('cheerio'); // 👈 para parsear HTML
//const { wrapper } = require('axios-cookiejar-support');

(async () => {
    const wrapper = (await import('axios-cookiejar-support')).wrapper;
    const jar = new tough.CookieJar();
    const client = wrapper(axios.create({ jar, withCredentials: true }));

    try {
        // 1. Faz GET para obter o HTML da página de login e extrair o targetId
        const loginPageRes = await client.get('https://bi.soeva.com.br/');
        const $ = cheerio.load(loginPageRes.data);
        const urlWithTargetId = loginPageRes.request.res.responseUrl;

        // 2. Extrai o targetId da URL (caso tenha vindo via redirecionamento)
        const targetIdMatch = urlWithTargetId.match(/targetId=([a-f0-9-]+)/i);
        const targetId = targetIdMatch ? targetIdMatch[1] : null;

        if (!targetId) throw new Error('targetId não encontrado');

        console.log('Target ID detectado:', targetId);

        // 3. POST com username, senha e targetId
        await client.post(`https://bi.soeva.com.br/internal_forms_authentication/?targetId=${targetId}`,
            new URLSearchParams({
                'username': 'SOEVA\\matheus.cavalcante',
                'pwd': '62yBR0k-21'
            }),
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );

        // 4. Acessa .qext após autenticação
        const qextResponse = await client.get('https://bi.soeva.com.br/extensions/_Teste/_Teste.qext');
        console.log('QEXT data:', qextResponse.data);
    } catch (error) {
        console.error('Erro:', error.response?.data || error.message);
    }
})();

//const cookieJar = new tough.CookieJar();
//const client = wrapper(axios.create({ jar: cookieJar, withCredentials: true }));

async function loginAndGetQext() {
    try {
        // 1. Acessa a página de login para pegar cookies iniciais (às vezes obrigatório)
        await client.get('https://bi.soeva.com.br');

        // 2. Faz login (ajuste os nomes dos campos se necessário)
        await client.post('https://bi.soeva.com.br/internal_forms_authentication/', new URLSearchParams({
            'username': 'SOEVA\\matheus.cavalcante',
            'pwd': '62yBR0k-21'
        }), {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        });

        // 3. Agora com a sessão válida, acessa o .qext
        const qextResponse = await client.get('https://bi.soeva.com.br/extensions/_Teste/_Teste.qext');

        console.log('QEXT data:', qextResponse.data);
    } catch (error) {
        console.error('Erro ao fazer login ou buscar qext:', error.response?.data || error.message);
    }
}

//loginAndGetQext();

// Configuração do Auth0
const checkJwt = jwt({
    secret: jwksRsa.expressJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`
    }),
    audience: process.env.AUTH0_AUDIENCE,
    issuer: `https://${process.env.AUTH0_DOMAIN}/`,
    algorithms: ['RS256']
});

// Middleware para checar permissões com RBAC
const checkPermissions = (permissions) => {
    return jwtAuthz(permissions, {
        customScopeKey: 'permissions',
        checkAllScopes: true
    });
};


function findPreviewImageBase64(dirPath) {
    try {
        function searchDirectory(currentPath) {
            const files = fs.readdirSync(currentPath, { withFileTypes: true });

            for (const file of files) {
                const fullPath = path.join(currentPath, file.name);

                if (file.isFile() && file.name.toLowerCase() === 'preview.png') {
                    const imageBuffer = fs.readFileSync(fullPath);
                    const base64Image = imageBuffer.toString('base64');
                    return `data:image/png;base64,${base64Image}`;
                }

                if (file.isDirectory()) {
                    const found = searchDirectory(fullPath);
                    if (found) return found;
                }
            }

            return null;
        }

        return searchDirectory(dirPath);

    } catch (error) {
        console.error('Erro ao buscar preview.png:', error);
        return null;
    }
}


// Função para obter os mashups disponíveis dinamicamente
const getMashupsList = () => {
    try {
        const allowedEndpoints = {};

        // Extrai o domínio da variável de ambiente (remove https://)
        const domainBase = process.env.AUTH0_AUDIENCE.replace('https://', '').replace('/', '');

        // Caminho para a pasta base dois níveis acima
        const basePath = path.resolve(__dirname, '../../');
        const domainPath = path.join(basePath, domainBase);

        logInfo(`Buscando mashups locais no caminho: ${domainPath}`);

        // Verifica se o diretório existe
        if (!fs.existsSync(domainPath)) {
            logWarning(`Diretório ${domainPath} não encontrado para mashups locais.`);
            return allowedEndpoints;
        }

        try {
            // Lista todos os diretórios dentro da pasta do domínio
            const mashupDirs = fs.readdirSync(domainPath, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            // Para cada diretório, procura o arquivo .qext
            const mashups = [];

            logInfo(`Encontrados ${mashupDirs.length} diretórios potenciais de mashup.`);

            for (const dir of mashupDirs) {
                const dirPath = path.join(domainPath, dir);
                const previewBase64 = findPreviewImageBase64(dirPath); // ou para Qlik Sense, adequar ao endpoint
                const qextFiles = fs.readdirSync(dirPath)
                    .filter(file => file.endsWith('.qext'));

                if (qextFiles.length > 0) {
                    try {
                        // Lê o arquivo .qext para obter o nome do mashup
                        const qextPath = path.join(dirPath, qextFiles[0]);
                        const qextContent = fs.readFileSync(qextPath, 'utf8');
                        const qextData = JSON.parse(qextContent);

                        mashups.push({
                            name: qextData.name || dir, // Usa o nome do diretório se o nome não for encontrado no .qext
                            path: `/${dir}/`,
                            type: 'local',
                            preview: previewBase64
                        });

                        logInfo(`Mashup local adicionado: ${qextData.name || dir}`);
                    } catch (error) {
                        logWarning(`Erro ao ler arquivo .qext em ${dir}:`, error.message);
                        // Adiciona usando o nome do diretório como fallback
                        mashups.push({
                            name: dir,
                            path: `/${dir}/`,
                            type: 'local',
                            preview: previewUrl
                        });

                        logInfo(`Mashup local adicionado (fallback): ${dir}`);
                    }
                } else {
                    // Se não encontrar arquivo .qext, usa o nome do diretório
                    mashups.push({
                        name: dir,
                        path: `/${dir}/`,
                        type: 'local'
                    });

                    logInfo(`Mashup local adicionado (sem .qext): ${dir}`);
                }
            }

            // Adiciona os mashups ao endpoint
            allowedEndpoints[process.env.AUTH0_AUDIENCE] = mashups;

            logSuccess(`Total de ${mashups.length} mashups locais encontrados para o endpoint ${process.env.AUTH0_AUDIENCE}`);

            return allowedEndpoints;
        } catch (error) {
            logError(`Erro ao ler diretório de mashups: ${domainPath}`, error);
            return allowedEndpoints;
        }
    } catch (error) {
        logError('Erro ao obter lista de mashups locais:', error);
        return {};
    }
};

const findSensePreviewImageBase64 = async (client, endpoint, references) => {
    try {
        const previewRef = references.find(ref =>
            ref.logicalPath.toLowerCase().endsWith('preview.png')
        );

        if (!previewRef) {
            return null;
        }

        const previewUrl = `${endpoint}${previewRef.externalPath}`;

        const response = await client.get(previewUrl, { responseType: 'arraybuffer' });

        const base64Image = `data:image/png;base64,${Buffer.from(response.data).toString('base64')}`;

        return base64Image;
    } catch (error) {
        logWarning('Erro ao buscar preview.png no Qlik Sense:', error.message);
        return null;
    }
};

// Função para obter os mashups do Qlik Sense Enterprise
const getQlikSenseMashups = async (endpoint, credentials) => {
    try {
        // Verificar se foram fornecidas credenciais
        if (!credentials || !credentials.username || !credentials.password) {
            logWarning('Credenciais não fornecidas para autenticação Qlik Sense Enterprise');
            return [];
        }

        const username = credentials.username;
        const password = credentials.password;

        logInfo(`Tentando autenticação para usuário: ${username}`);

        // Remover protocolo para o nome da pasta de certificados
        const endpointFolderName = endpoint.replace('https://', '').replace('http://', '');
        logInfo(`Buscando mashups do Qlik Sense Enterprise para o endpoint: ${endpoint}`);

        // Usar cookie jar para autenticação baseada em formulário
        const wrapper = (await import('axios-cookiejar-support')).wrapper;
        const tough = require('tough-cookie');
        const jar = new tough.CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: 30000, // Timeout maior para a operação
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        }));

        try {
            // 1. Faz GET para obter o HTML da página de login e extrair o targetId
            logInfo(`Obtendo página de login: ${endpoint}`);
            const loginPageRes = await client.get(endpoint);
            const cheerio = require('cheerio');
            const $ = cheerio.load(loginPageRes.data);
            const urlWithTargetId = loginPageRes.request.res.responseUrl;

            // 2. Extrai o targetId da URL (caso tenha vindo via redirecionamento)
            const targetIdMatch = urlWithTargetId.match(/targetId=([a-f0-9-]+)/i);
            const targetId = targetIdMatch ? targetIdMatch[1] : null;

            if (!targetId) {
                logWarning('targetId não encontrado na URL de redirecionamento');
                // Tentar encontrar no HTML
                const targetIdInput = $('input[name="targetId"]').val();
                if (targetIdInput) {
                    logInfo(`targetId encontrado no formulário HTML: ${targetIdInput}`);
                    targetId = targetIdInput;
                } else {
                    throw new Error('targetId não encontrado');
                }
            }

            logInfo(`Target ID detectado: ${targetId}`);

            // 3. POST com username, senha e targetId
            logInfo('Enviando credenciais para autenticação');
            try {
                logInfo('Enviando credenciais para autenticação');

                const responseForAuth = await client.post(`${endpoint}/internal_forms_authentication/?targetId=${targetId}`,
                    new URLSearchParams({
                        'username': username,
                        'pwd': password
                    }),
                    {
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                    }
                );

                const $ = cheerio.load(responseForAuth.data);

                // Extrai mensagem de erro da div de erro, se existir
                const errorMessage = $('#error-message').text().trim();

                // Verifica se existe erro no conteúdo HTML
                if (errorMessage) {
                    logWarning(`Erro na resposta da autenticação: ${errorMessage}`);

                    let error;

                    if (errorMessage.includes("The following user is not authenticated")) {
                        error = new Error('auth_failed');
                        error.details = 'Usuário não autenticado';
                        error.code = 'USER_NOT_AUTHENTICATED';
                    } else if (errorMessage.includes("could not be identified")) {
                        error = new Error('auth_failed');
                        error.details = 'Usuário não encontrado no domínio';
                        error.code = 'USER_NOT_FOUND';
                    } else if (errorMessage.includes("could not be authenticated")) {
                        error = new Error('auth_failed');
                        error.details = 'Senha incorreta';
                        error.code = 'INVALID_PASSWORD';
                    } else if (errorMessage.includes("account is locked") || errorMessage.includes("conta está bloqueada")) {
                        error = new Error('auth_failed');
                        error.details = 'Conta bloqueada. Entre em contato com o administrador.';
                        error.code = 'ACCOUNT_LOCKED';
                    } else if (errorMessage.includes("password has expired") || errorMessage.includes("senha expirou")) {
                        error = new Error('auth_failed');
                        error.details = 'Sua senha expirou. Redefina sua senha pelo Portal do Qlik Sense.';
                        error.code = 'PASSWORD_EXPIRED';
                    } else {
                        error = new Error('auth_failed');
                        error.details = 'Usuário ou senha inválidos';
                        error.code = 'AUTHENTICATION_FAILED';
                    }

                    throw error;
                }

                // Caso não haja erro, segue normalmente
                $('img').remove();
                const bodyContent = $('body').html();

                console.log('Body do HTML retornado:', bodyContent);
            } catch (authError) {
                logError(`Erro durante a autenticação:`, authError.message);
                throw authError.message;
            }

            // 4. Obter a lista de extensões
            logInfo('Obtendo lista de extensões');
            const extensionsResponse = await client.get(`${endpoint}/qrs/extension/full`, {
                headers: {
                    "X-Qlik-Xrfkey": "1234567890123456"
                },
                params: {
                    "xrfkey": "1234567890123456"
                }
            });

            if (!extensionsResponse.data || !Array.isArray(extensionsResponse.data)) {
                logWarning('Resposta da API de extensões não contém um array');
                return [];
            }

            // Lista para armazenar os mashups verificados
            const verifiedMashups = [];

            // 5. Para cada extensão, verificar o arquivo .qext
            for (const extension of extensionsResponse.data) {

                const previewBase64 = await findSensePreviewImageBase64(client, endpoint, extension.references);


                try {
                    // Verificar se a extensão tem arquivo .qext
                    if (!Array.isArray(extension.references)) continue;

                    // Buscar referência .qext
                    const qextRef = extension.references.find(ref =>
                        ref.logicalPath.toLowerCase().endsWith('.qext')
                    );

                    if (!qextRef) continue;

                    // Verificar também se há arquivo .html
                    const hasHtml = extension.references.some(ref =>
                        ref.logicalPath.toLowerCase().endsWith('.html')
                    );

                    if (!hasHtml) continue;

                    // Obter caminho para o arquivo .qext
                    const qextPath = qextRef.externalPath;
                    const qextUrl = `${endpoint}${qextPath}`;

                    logInfo(`Verificando arquivo .qext: ${qextUrl}`);

                    // Obter conteúdo do arquivo .qext
                    const qextResponse = await client.get(qextUrl);
                    let qextData;

                    try {
                        // Tentar fazer parse do JSON
                        qextData = typeof qextResponse.data === 'string'
                            ? JSON.parse(qextResponse.data)
                            : qextResponse.data;
                    } catch (jsonError) {
                        logWarning(`Erro ao parsear JSON do arquivo .qext: ${qextUrl}`);
                        continue;
                    }

                    // Verificar se o tipo é mashup
                    if (qextData && qextData.type === 'mashup') {
                        logSuccess(`Mashup encontrado: ${extension.name}`);

                        // Buscar referência .html para o caminho
                        const htmlRef = extension.references.find(ref =>
                            ref.logicalPath.toLowerCase().endsWith('.html')
                        );

                        verifiedMashups.push({
                            name: qextData.name || extension.name,
                            description: qextData.description || '',
                            path: htmlRef ? htmlRef.externalPath : `/extensions/${extension.name}/`,
                            id: extension.id,
                            type: 'qlikSense',
                            endpoint: endpoint,
                            preview: previewBase64
                        });
                    }
                } catch (extensionError) {
                    logWarning(`Erro ao processar extensão ${extension.name}: ${extensionError.message}`);
                    continue;
                }
            }

            // Se encontramos mashups, retorne-os
            if (verifiedMashups.length > 0) {
                logSuccess(`${verifiedMashups.length} mashups verificados encontrados no Qlik Sense Enterprise`);
                return verifiedMashups;
            }

            // Se não encontrou nenhum mashup verificado, pelo menos retorne o Hub
            logWarning('Nenhum mashup verificado encontrado. Retornando Hub como fallback.');
            return [{
                name: 'QlikSense Hub',
                path: '/hub/',
                id: 'hub',
                type: 'qlikSense',
                endpoint: endpoint,
                dummy: true
            }];

        } catch (authError) {
            logError(`Erro durante a autenticação no Qlik Sense Enterprise:`, authError.message);
            throw authError.message;
        }

    } catch (error) {
        logError(`Erro crítico ao obter mashups do Qlik Sense Enterprise (${endpoint}):`, error);
        throw error; // <-- deixa o erro subir para o catch do endpoint principal
    }
};

// Endpoint para listar mashups disponíveis com base no RBAC
app.get('/api/mashups', checkJwt, async (req, res) => {
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    const username = req.query.username;
    const password = req.query.password;
    const credentials = { username, password };

    logInfo(`[${requestId}] Nova requisição para /api/mashups`);

    try {
        // Obtém as permissões do usuário do token JWT
        const userPermissions = req.auth.permissions || [];
        logInfo(`[${requestId}] Permissões do usuário:`, userPermissions);

        // Função auxiliar para verificar permissões de maneira mais flexível
        const hasPermissionForEndpoint = (endpoint, permissions) => {
            // Normaliza o endpoint para comparação (remove https:// e /)
            const normalizedEndpoint = endpoint.replace('https://', '').replace('http://', '').replace(/\/+$/, '');

            // Verifica permissão completa ou parcial
            const hasDirectPermission = permissions.some(permission => {
                // Diferentes formatos possíveis de permissão
                const formats = [
                    `access:${endpoint}`,                // Formato completo com https://
                    `access:${normalizedEndpoint}`,      // Formato sem https://
                    endpoint,                            // Apenas o endpoint
                    normalizedEndpoint                   // Apenas o endpoint normalizado
                ];

                return formats.some(format => permission.includes(format));
            });

            // Admin tem acesso a tudo
            const isAdmin = permissions.includes('admin:all');

            return hasDirectPermission || isAdmin;
        };

        // Obtém a lista dinâmica de mashups locais
        logInfo(`[${requestId}] Buscando mashups locais...`);
        const allowedEndpoints = getMashupsList();

        // Verificar quais endpoints de Qlik Sense o usuário tem acesso
        const availableMashups = [];

        // Adicionar mashups locais
        logInfo(`[${requestId}] Verificando permissões para mashups locais...`);

        for (const endpoint in allowedEndpoints) {
            // Verifica se o usuário tem permissão usando o método flexível
            const hasEndpointAccess = hasPermissionForEndpoint(endpoint, userPermissions);
            const isAdmin = userPermissions.includes('admin:all');

            if (hasEndpointAccess || isAdmin) {
                logInfo(`[${requestId}] Usuário tem acesso ao endpoint: ${endpoint} (${hasEndpointAccess ? 'permissão direta' : 'admin'})`);
                availableMashups.push(...allowedEndpoints[endpoint]);
                logInfo(`[${requestId}] Adicionados ${allowedEndpoints[endpoint].length} mashups locais`);
            } else {
                logInfo(`[${requestId}] Usuário não tem permissão para acessar o endpoint: ${endpoint}`);
            }
        }

        // Verificar e adicionar mashups do Qlik Sense Enterprise
        const qlikEndpoints = [
            'https://bi.soeva.com.br'
            // Adicionar outros endpoints conforme necessário
        ];

        logInfo(`[${requestId}] Verificando permissões para endpoints Qlik Sense Enterprise...`);

        for (const endpoint of qlikEndpoints) {
            // Verificar se o usuário tem permissão usando o método flexível
            const hasQlikEndpointAccess = hasPermissionForEndpoint(endpoint, userPermissions);
            const isAdmin = userPermissions.includes('admin:all');

            if (hasQlikEndpointAccess || isAdmin) {
                logInfo(`[${requestId}] Usuário tem acesso ao endpoint Qlik: ${endpoint} (${hasQlikEndpointAccess ? 'permissão direta' : 'admin'})`);

                // Verificar credenciais para o Qlik Sense Enterprise
                if (!credentials || !credentials.username || credentials.username === 'undefined' ||
                    !credentials.password || credentials.password === 'undefined') {
                    logInfo(`[${requestId}] Credenciais não fornecidas para Qlik Sense Enterprise`);
                    return res.status(401).json({
                        requiresEnterpriseLogin: true,
                        message: 'Mashups do Qlik Enterprise detectados. É necessário login.'
                    });
                }

                try {
                    // Buscar mashups do Qlik Sense para este endpoint
                    const qlikMashups = await getQlikSenseMashups(endpoint, credentials);

                    if (qlikMashups && qlikMashups.length > 0) {
                        availableMashups.push(...qlikMashups);
                        logInfo(`[${requestId}] Adicionados ${qlikMashups.length} mashups do Qlik Sense Enterprise`);
                    } else {
                        logWarning(`[${requestId}] Nenhum mashup encontrado no Qlik Sense Enterprise`);
                    }
                } catch (qlikError) {
                    if (qlikError.message && qlikError.message.includes('targetId')) {
                        // Erro específico de autenticação
                        logWarning(`[${requestId}] Erro de autenticação no Qlik Sense Enterprise`);
                        return res.status(401).json({
                            requiresEnterpriseLogin: true,
                            message: 'Falha na autenticação do Qlik Enterprise. Verifique suas credenciais.',
                            errorCode: 'TARGET_ID_ERROR'
                        });
                    } else if (qlikError.message === 'auth_failed') {
                        // Credenciais inválidas com detalhes mais específicos
                        logWarning(`[${requestId}] Credenciais inválidas para Qlik Sense Enterprise: ${qlikError.code || 'UNKNOWN'}`);
                        return res.status(401).json({
                            requiresEnterpriseLogin: true,
                            message: qlikError.details || 'Usuário ou senha inválidos',
                            errorCode: qlikError.code || 'AUTHENTICATION_FAILED',
                            authFailed: true
                        });
                    } else {
                        // Outros erros
                        logError(`[${requestId}] Erro ao buscar mashups do Qlik Sense Enterprise:`, qlikError);
                    }
                }
            } else {
                logInfo(`[${requestId}] Usuário não tem permissão para acessar o endpoint Qlik: ${endpoint}`);
            }
        }

        logSuccess(`[${requestId}] Total de ${availableMashups.length} mashups retornados na resposta`);
        return res.json(availableMashups);
    } catch (error) {
        logError(`[${requestId}] Erro ao processar a solicitação de mashups:`, error);
        return res.status(500).json({ error: 'Erro ao processar a solicitação' });
    }
});

// Nova rota para validar credenciais do Qlik Enterprise
app.post('/api/validate-enterprise', checkJwt, async (req, res) => {
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    const { username, password } = req.body;

    logInfo(`[${requestId}] Nova requisição para validar credenciais do Qlik Enterprise`);

    if (!username || !password) {
        return res.status(400).json({
            success: false,
            message: 'Usuário e senha são obrigatórios'
        });
    }

    try {
        // Verificamos apenas no primeiro endpoint do Qlik Sense Enterprise
        const endpoint = 'https://bi.soeva.com.br';

        // Usar cookie jar para autenticação baseada em formulário
        const wrapper = (await import('axios-cookiejar-support')).wrapper;
        const tough = require('tough-cookie');
        const jar = new tough.CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: 30000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        }));

        try {
            // 1. Faz GET para obter o HTML da página de login e extrair o targetId
            logInfo(`[${requestId}] Obtendo página de login: ${endpoint}`);
            const loginPageRes = await client.get(endpoint);
            const cheerio = require('cheerio');
            const $ = cheerio.load(loginPageRes.data);
            const urlWithTargetId = loginPageRes.request.res.responseUrl;

            // 2. Extrai o targetId da URL (caso tenha vindo via redirecionamento)
            const targetIdMatch = urlWithTargetId.match(/targetId=([a-f0-9-]+)/i);
            let targetId = targetIdMatch ? targetIdMatch[1] : null;

            if (!targetId) {
                logWarning(`[${requestId}] targetId não encontrado na URL de redirecionamento`);
                // Tentar encontrar no HTML
                const targetIdInput = $('input[name="targetId"]').val();
                if (targetIdInput) {
                    logInfo(`[${requestId}] targetId encontrado no formulário HTML: ${targetIdInput}`);
                    targetId = targetIdInput;
                } else {
                    throw new Error('targetId não encontrado');
                }
            }

            logInfo(`[${requestId}] Target ID detectado: ${targetId}`);

            // 3. POST com username, senha e targetId
            logInfo(`[${requestId}] Enviando credenciais para autenticação`);
            try {
                const responseForAuth = await client.post(`${endpoint}/internal_forms_authentication/?targetId=${targetId}`,
                    new URLSearchParams({
                        'username': username,
                        'pwd': password
                    }),
                    {
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                    }
                );

                const $ = cheerio.load(responseForAuth.data);

                // Extrai mensagem de erro da div de erro, se existir
                const errorMessage = $('#error-message').text().trim();

                // Verifica se existe erro no conteúdo HTML
                if (errorMessage) {
                    logWarning(`Erro na resposta da autenticação: ${errorMessage}`);

                    let error;

                    if (errorMessage.includes("The following user is not authenticated")) {
                        error = new Error('auth_failed');
                        error.details = 'Usuário não autenticado';
                        error.code = 'USER_NOT_AUTHENTICATED';
                    } else if (errorMessage.includes("could not be identified")) {
                        error = new Error('auth_failed');
                        error.details = 'Usuário não encontrado no domínio';
                        error.code = 'USER_NOT_FOUND';
                    } else if (errorMessage.includes("could not be authenticated")) {
                        error = new Error('auth_failed');
                        error.details = 'Senha incorreta';
                        error.code = 'INVALID_PASSWORD';
                    } else if (errorMessage.includes("account is locked") || errorMessage.includes("conta está bloqueada")) {
                        error = new Error('auth_failed');
                        error.details = 'Conta bloqueada. Entre em contato com o administrador.';
                        error.code = 'ACCOUNT_LOCKED';
                    } else if (errorMessage.includes("password has expired") || errorMessage.includes("senha expirou")) {
                        error = new Error('auth_failed');
                        error.details = 'Sua senha expirou. Redefina sua senha pelo Portal do Qlik Sense.';
                        error.code = 'PASSWORD_EXPIRED';
                    } else {
                        error = new Error('auth_failed');
                        error.details = 'Usuário ou senha inválidos';
                        error.code = 'AUTHENTICATION_FAILED';
                    }

                    throw error;
                }

                // Caso não haja erro, segue normalmente
                $('img').remove();
                const bodyContent = $('body').html();

                console.log('Body do HTML retornado:', bodyContent);

                // Se chegou aqui, a autenticação foi bem-sucedida
                logSuccess(`[${requestId}] Autenticação bem-sucedida para o usuário: ${username}`);
                return res.json({
                    success: true,
                    message: 'Autenticação bem-sucedida',
                    authQlik: true
                });

            } catch (authError) {
                logError(`Erro durante a autenticação:`, authError.message);
                throw authError.message;
            }
        } catch (error) {
            logError(`[${requestId}] Erro ao acessar o endpoint Qlik Sense Enterprise:`, error.message);
            return res.status(500).json({
                success: false,
                message: error.message === 'targetId não encontrado'
                    ? 'Falha ao acessar o servidor Qlik Enterprise'
                    : 'Erro ao processar a requisição'
            });
        }
    } catch (error) {
        logError(`[${requestId}] Erro geral na validação:`, error);
        return res.status(500).json({
            success: false,
            message: 'Erro ao processar a requisição'
        });
    }
});

// Rota de verificação de saúde do servidor
app.get('/health', (req, res) => {
    logInfo('Verificação de saúde do servidor realizada');
    res.json({ status: 'ok' });
});

// Rota de diagnóstico para verificar as permissões do token
app.get('/api/auth/debug', checkJwt, (req, res) => {
    try {
        const requestId = req.requestId;
        logInfo(`[${requestId}] Nova requisição para /api/auth/debug`);

        // Informações básicas do token
        const tokenInfo = {
            permissions: req.auth.permissions || [],
            sub: req.auth.sub,
            iss: req.auth.iss,
            aud: req.auth.aud,
            exp: req.auth.exp,
            iat: req.auth.iat
        };

        // Verificar acesso aos endpoints comuns
        const endpoints = [
            process.env.AUTH0_AUDIENCE,
            'https://bi.soeva.com.br'
        ];

        const accessCheck = {};

        // Função auxiliar para verificar permissões
        const hasPermissionForEndpoint = (endpoint, permissions) => {
            const normalizedEndpoint = endpoint.replace('https://', '').replace('http://', '').replace(/\/+$/, '');

            const hasDirectPermission = permissions.some(permission => {
                const formats = [
                    `access:${endpoint}`,
                    `access:${normalizedEndpoint}`,
                    endpoint,
                    normalizedEndpoint
                ];

                return formats.some(format => permission.includes(format));
            });

            return hasDirectPermission;
        };

        // Verificar cada endpoint
        for (const endpoint of endpoints) {
            accessCheck[endpoint] = {
                hasAccess: hasPermissionForEndpoint(endpoint, tokenInfo.permissions) || tokenInfo.permissions.includes('admin:all'),
                viaAdmin: tokenInfo.permissions.includes('admin:all')
            };
        }

        // Resposta completa
        const response = {
            tokenInfo,
            accessCheck,
            allPermissions: tokenInfo.permissions,
            timestamp: new Date().toISOString()
        };

        logSuccess(`[${requestId}] Informações de depuração do token geradas com sucesso`);
        return res.json(response);
    } catch (error) {
        logError('Erro ao processar informações de depuração do token:', error);
        return res.status(500).json({ error: 'Erro ao processar informações de depuração do token' });
    }
});

// Rotas de usuários
app.use('/api/users', userRoutes);

// Middleware de tratamento de erros
app.use(logErrorHandler);

// Inicia o servidor
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    logSuccess(`Servidor rodando na porta ${PORT}`);
    logInfo('Servidor pronto para processar requisições');
}); 