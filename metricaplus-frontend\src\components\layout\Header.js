import React, { useState, useEffect, useRef } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { Link } from 'react-router-dom';
import userImg from '../../assets/img/userImg.png';
import { FaBars } from 'react-icons/fa';
import './Header.css';

const Header = ({ pageTitle, expanded, setExpanded, manuallyToggled, setManuallyToggled }) => {
  const { user, logout } = useAuth0();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const profileMenuRef = useRef(null);
  const notificationsRef = useRef(null);

  // Simular notificações para demonstração
  useEffect(() => {
    setNotifications([
      {
        id: 1,
        type: 'info',
        message: 'Bem-vindo ao novo dashboard MetricaPlus!',
        time: '2 minutos atrás',
        read: false
      },
      {
        id: 2,
        type: 'success',
        message: 'Seu relatório foi gerado com sucesso',
        time: '1 hora atrás',
        read: false
      },
      {
        id: 3,
        type: 'warning',
        message: 'Lembrete: atualizar suas configurações',
        time: '3 horas atrás',
        read: true
      }
    ]);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('enterpriseCredentials');
    localStorage.removeItem('metricaplus_mashups_cache');
    localStorage.removeItem('metricaplus_mashups_timestamp');
    logout({
      returnTo: window.location.origin,
      federated: true,
    });
  };

  const markAsRead = (id) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const toggleSidebar = () => {
    const newExpanded = !expanded;
    setExpanded(newExpanded);
    setManuallyToggled(true);
  };

  // Fechar menus quando clicar fora deles
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        setShowProfileMenu(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const renderNotificationIcon = (type) => {
    switch(type) {
      case 'info':
        return (
          <svg className="notification-icon info" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="16" x2="12" y2="12" />
            <line x1="12" y1="8" x2="12.01" y2="8" />
          </svg>
        );
      case 'success':
        return (
          <svg className="notification-icon success" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="notification-icon warning" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <header className="app-header">

      <FaBars className='toggle-sidebar-button-mobile' onClick={toggleSidebar} />
      <div className="header-left">        
        <h1 className="page-title">{pageTitle}</h1>
      </div>
      
      <div className="header-right">
        <div className="header-search">
          <div className="search-container">
            <svg className="search-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
            </svg>
            <input type="text" placeholder="Buscar..." className="search-input" />
          </div>
        </div>
        
        <div className="header-notifications" ref={notificationsRef}>
          <button 
            className="notification-button" 
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
              <path d="M13.73 21a2 2 0 0 1-3.46 0" />
            </svg>
            {unreadCount > 0 && (
              <span className="notification-badge">{unreadCount}</span>
            )}
          </button>
          
          {showNotifications && (
            <div className="notifications-dropdown">
              <div className="notifications-header">
                <h3>Notificações</h3>
                <button className="mark-all-read" onClick={markAllAsRead}>
                  Marcar todas como lidas
                </button>
              </div>
              
              <div className="notifications-list">
                {notifications.length > 0 ? (
                  notifications.map(notification => (
                    <div 
                      key={notification.id} 
                      className={`notification-item ${notification.read ? 'read' : 'unread'}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="notification-icon-container">
                        {renderNotificationIcon(notification.type)}
                      </div>
                      <div className="notification-content">
                        <p className="notification-text">{notification.message}</p>
                        <span className="notification-time">{notification.time}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-notifications">
                    <p>Não há notificações</p>
                  </div>
                )}
              </div>
              
              <div className="notifications-footer">
                <Link to="/notifications" className="view-all-link">
                  Ver todas
                </Link>
              </div>
            </div>
          )}
        </div>
        
        <div className="header-profile" ref={profileMenuRef}>
          <button 
            className="profile-button" 
            onClick={() => setShowProfileMenu(!showProfileMenu)}
          >
            <img 
              src={user?.picture || userImg} 
              alt={user?.name || 'Perfil'} 
              className="profile-avatar" 
              referrerPolicy="no-referrer"
            />
            <span className="profile-name">{user?.name}</span>
            <svg className={`dropdown-arrow ${showProfileMenu ? 'open' : ''}`} xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </button>
          
          {showProfileMenu && (
            <div className="profile-dropdown">
              <div className="profile-header">
                <img 
                  src={user?.picture || userImg} 
                  alt={user?.name || 'Perfil'} 
                  className="dropdown-avatar"
                  referrerPolicy="no-referrer"
                />
                <div className="dropdown-user-info">
                  <h4 className="dropdown-name">{user?.name}</h4>
                  <p className="dropdown-email">{user?.email}</p>
                </div>
              </div>
              
              <ul className="profile-menu">
                <li className="profile-menu-item">
                  <Link to="/profile" className="profile-menu-link">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                      <circle cx="12" cy="7" r="4" />
                    </svg>
                    <span>Meu Perfil</span>
                  </Link>
                </li>
                <li className="profile-menu-item">
                  <Link to="/settings" className="profile-menu-link">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="3" />
                      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
                    </svg>
                    <span>Configurações</span>
                  </Link>
                </li>
                <li className="profile-menu-item">
                  <Link to="/debug" className="profile-menu-link">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                    </svg>
                    <span>Diagnóstico</span>
                  </Link>
                </li>
                <div className="menu-divider"></div>
                <li className="profile-menu-item">
                  <button className="profile-menu-link logout-link" onClick={handleLogout}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                      <polyline points="16 17 21 12 16 7" />
                      <line x1="21" y1="12" x2="9" y2="12" />
                    </svg>
                    <span>Sair</span>
                  </button>
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header; 