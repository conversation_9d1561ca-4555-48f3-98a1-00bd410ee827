.login-page {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  position: relative;
  overflow: hidden;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  pointer-events: none;
}

@keyframes float {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(0px) translateX(20px);
  }
  75% {
    transform: translateY(20px) translateX(10px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

.login-container {
  display: flex;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  width: 80%;
  max-width: 1100px;
  min-height: 600px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.login-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 3rem 2rem;
  max-width: 500px;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo-icon {
  width: 60px;
  height: 60px;
  color: var(--primary-color);
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.login-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.login-subtitle {
  font-size: 1rem;
  color: #666;
  margin-top: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.login-divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: #999;
  font-size: 0.85rem;
}

.login-divider::before,
.login-divider::after {
  content: "";
  flex-grow: 1;
  background: #eee;
  height: 1px;
  margin: 0 1rem;
}

.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(200, 100, 0, 0.2);
}

.login-button:hover {
  background-color: var(--accent-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(200, 100, 0, 0.3);
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(200, 100, 0, 0.2);
}

.login-icon {
  width: 24px;
  height: 24px;
}

.login-features {
  margin-top: 3rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 1rem;
  background: #f5f7fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: #e8eaf6;
  transform: translateX(5px);
}

.feature-icon {
  color: var(--primary-color);
}

.feature-text {
  font-size: 0.95rem;
  color: #333;
}

.login-footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.8rem;
  color: #aaa;
}

.login-image-container {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.login-image {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-preview {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  opacity: 0;
  animation: fadeInUp 1s ease forwards;
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-header {
  background: #f5f7fa;
  padding: 1rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.preview-dots {
  display: flex;
  gap: 6px;
  margin-right: 15px;
}

.preview-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.preview-dots span:nth-child(1) {
  background-color: #ff5f56;
}

.preview-dots span:nth-child(2) {
  background-color: #ffbd2e;
}

.preview-dots span:nth-child(3) {
  background-color: #27c93f;
}

.preview-title {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.preview-content {
  padding: 1.5rem;
  height: 300px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-chart {
  height: 150px;
  background: linear-gradient(45deg, #e8eaf6, #f5f7fa);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.preview-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 0%, transparent 10%, #c5cae9 10%, #c5cae9 12%, transparent 12%, transparent 88%, #c5cae9 88%, #c5cae9 90%, transparent 90%, transparent 100%),
    linear-gradient(transparent 0%, transparent 20%, #c5cae9 20%, #c5cae9 22%, transparent 22%, transparent 78%, #c5cae9 78%, #c5cae9 80%, transparent 80%, transparent 100%);
}

.preview-chart::after {
  content: '';
  position: absolute;
  bottom: 15%;
  left: 5%;
  width: 90%;
  height: 30%;
  background: var(--accent-color);
  clip-path: polygon(0% 100%, 10% 60%, 20% 90%, 30% 30%, 40% 70%, 50% 45%, 60% 0%, 70% 30%, 80% 60%, 90% 10%, 100% 40%, 100% 100%);
  opacity: 0.7;
  animation: chartAnimation 8s infinite;
}

@keyframes chartAnimation {
  0% {
    clip-path: polygon(0% 100%, 10% 60%, 20% 90%, 30% 30%, 40% 70%, 50% 45%, 60% 0%, 70% 30%, 80% 60%, 90% 10%, 100% 40%, 100% 100%);
  }
  50% {
    clip-path: polygon(0% 100%, 10% 40%, 20% 70%, 30% 60%, 40% 20%, 50% 65%, 60% 30%, 70% 10%, 80% 40%, 90% 60%, 100% 20%, 100% 100%);
  }
  100% {
    clip-path: polygon(0% 100%, 10% 60%, 20% 90%, 30% 30%, 40% 70%, 50% 45%, 60% 0%, 70% 30%, 80% 60%, 90% 10%, 100% 40%, 100% 100%);
  }
}

.preview-bars {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  height: 60px;
}

.preview-bars span {
  flex: 1;
  background: #e8eaf6;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.preview-bars span::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: var(--accent-color);
  animation: barAnimation 3s infinite;
}

.preview-bars span:nth-child(1)::after {
  height: 70%;
  animation-delay: 0.2s;
}

.preview-bars span:nth-child(2)::after {
  height: 40%;
  animation-delay: 0.4s;
}

.preview-bars span:nth-child(3)::after {
  height: 90%;
  animation-delay: 0.6s;
}

.preview-bars span:nth-child(4)::after {
  height: 60%;
  animation-delay: 0.8s;
}

@keyframes barAnimation {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
  }
}

.preview-metrics {
  display: flex;
  gap: 15px;
}

.preview-metric {
  flex: 1;
  height: 40px;
  background: #e8eaf6;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.preview-metric::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
  width: 70%;
  height: 10px;
  background: #c5cae9;
  border-radius: 5px;
}

/* Loading spinner */
.login-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  color: white;
}

.login-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsividade */
@media (max-width: 992px) {
  .login-container {
    width: 90%;
    max-width: 600px;
    flex-direction: column;
  }
  
  .login-card {
    max-width: none;
    padding: 2rem;
  }
  
  .login-image-container {
    display: none;
  }
  
  .login-features {
    margin-top: 2rem;
  }
} 