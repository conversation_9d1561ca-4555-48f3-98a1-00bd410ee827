import React, { useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { Navigate } from 'react-router-dom';
import Auth0Logo from '../../assets/icons/Auth0Logo';
import LogoMetricaPlus from '../../assets/icons/logoMetricaPlus_simbol';
import logoMetricaPlus_name from '../../assets/img/logoMetricaPlus_name.png';
import './Login.css';

// Logo Component
const Logo = () => (
  <svg className="login-logo-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" />
  </svg>
);

// Auth0 Login Button
const LoginButton = () => {
  const { loginWithRedirect } = useAuth0();
  return (
    <button
      onClick={() => loginWithRedirect()}
      className="login-button"
    >
      <Auth0Logo />
      <span>Entrar com Auth0</span>
    </button>
  );
};

// Particle Animation Effects
const ParticleEffect = () => {
  useEffect(() => {
    const createParticle = () => {
      const particles = document.querySelector('.particles');
      if (!particles) return;

      for (let i = 0; i < 60; i++) {
        const particle = document.createElement('div');
        particle.classList.add('particle');
        
        // Posição aleatória
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        
        // Tamanho aleatório
        const size = Math.random() * 10 + 5;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        
        // Opacidade aleatória
        particle.style.opacity = Math.random() * 0.5 + 0.1;
        
        // Velocidade aleatória
        const duration = Math.random() * 20 + 10;
        particle.style.animation = `float ${duration}s linear infinite`;
        
        // Adiciona ao DOM
        particles.appendChild(particle);
      }
    };

    // Cria as partículas quando o componente é montado
    createParticle();

    // Limpa as partículas quando o componente é desmontado
    return () => {
      const particles = document.querySelector('.particles');
      if (particles) {
        particles.innerHTML = '';
      }
    };
  }, []);

  return <div className="particles"></div>;
};

const Login = () => {
  const { isAuthenticated, isLoading } = useAuth0();

  if (isLoading) {
    return (
      <div className="login-loading">
        <div className="login-spinner"></div>
        <p>Carregando...</p>
      </div>
    );
  }

  if (isAuthenticated) {
    return <Navigate to="/" />;
  }

  return (
    <div className="login-page">
      <ParticleEffect />
      
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <LogoMetricaPlus className="login-logo-icon" width="24" height="24" />
            <img src={logoMetricaPlus_name} alt="Logo MetricaPlus" className="login-logo-name" width="200" />
            <p className="login-subtitle">Acesse mashups e dashboards personalizados</p>
          </div>
          
          <div className="login-form">
            <div className="login-divider">
              <span>Login</span>
            </div>
            
            <LoginButton />
            
            <div className="login-features">
              <div className="feature-item">
                <svg className="feature-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                </svg>
                <span className="feature-text">Analytics em tempo real</span>
              </div>
              
              <div className="feature-item">
                <svg className="feature-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
                <span className="feature-text">Mashups personalizados</span>
              </div>
              
              <div className="feature-item">
                <svg className="feature-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span className="feature-text">Relatórios agendados</span>
              </div>
            </div>
          </div>
          
          <footer className="login-footer">
            <p>© {new Date().getFullYear()} MetricaPlus - Todos os direitos reservados</p>
          </footer>
        </div>
        
        <div className="login-image-container">
          <div className="login-image">
            <div className="dashboard-preview">
              <div className="preview-header">
                <div className="preview-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div className="preview-title">Dashboard</div>
              </div>
              <div className="preview-content">
                <div className="preview-chart"></div>
                <div className="preview-bars">
                  <span></span>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div className="preview-metrics">
                  <div className="preview-metric"></div>
                  <div className="preview-metric"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login; 