import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import userImg from '../../assets/img/userImg.png';
import './AuthDebug.css';

// Componente de LoadingSpinner
import LoadingSpinner from '../common/LoadingSpinner';

const AuthDebug = () => {
  const { getAccessTokenSilently, user } = useAuth0();
  const [tokenInfo, setTokenInfo] = useState(null);
  const [tokenRaw, setTokenRaw] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugResponse, setDebugResponse] = useState(null);
  const [activeTab, setActiveTab] = useState('user');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const fetchTokenInfo = async () => {
      try {
        // Obtém o token de acesso
        const token = await getAccessTokenSilently({
          audience: process.env.REACT_APP_AUTH0_AUDIENCE,
          scope: 'read:mashups'
        });

        setTokenRaw(token);

        // Decodificar o token para exibir informações (apenas para frontend)
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          try {
            const payload = JSON.parse(atob(tokenParts[1]));
            setTokenInfo(payload);
          } catch (e) {
            console.error('Erro ao decodificar token:', e);
          }
        }

        // Fazer a chamada para o endpoint de debug
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/auth/debug`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        setDebugResponse(response.data);
        setLoading(false);
      } catch (error) {
        console.error('Erro ao buscar informações do token:', error);
        setError('Não foi possível carregar as informações do token');
        setLoading(false);
      }
    };

    fetchTokenInfo();
  }, [getAccessTokenSilently]);

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-icon">⚠️</div>
        <div className="error-message">{error}</div>
      </div>
    );
  }

  const renderTabContent = () => {
    switch(activeTab) {
      case 'user':
        return (
          <div className="tab-content">
            <div className="user-profile-card">
              <div className="user-profile-header">
                <img src={user.picture || userImg} alt={user.name} className="user-avatar" referrerPolicy="no-referrer" />
                <div className="user-details">
                  <h3 className="user-name">{user.name}</h3>
                  <span className="user-email">{user.email}</span>
                </div>
              </div>
              <div className="user-metadata">
                <div className="metadata-item">
                  <span className="metadata-label">ID</span>
                  <span className="metadata-value">{user.sub}</span>
                </div>
                <div className="metadata-item">
                  <span className="metadata-label">E-mail Verificado</span>
                  <span className="metadata-value">{user.email_verified ? 'Sim' : 'Não'}</span>
                </div>
                {user.updated_at && (
                  <div className="metadata-item">
                    <span className="metadata-label">Última Atualização</span>
                    <span className="metadata-value">{new Date(user.updated_at).toLocaleString()}</span>
                  </div>
                )}
              </div>
              <div className="user-json">
                <div className="json-header">
                  <h4>Dados Completos</h4>
                  <button className="copy-button" onClick={() => copyToClipboard(JSON.stringify(user, null, 2))}>
                    {copied ? 'Copiado!' : 'Copiar'}
                  </button>
                </div>
                <pre>{JSON.stringify(user, null, 2)}</pre>
              </div>
            </div>
          </div>
        );
      
      case 'token':
        return (
          <div className="tab-content">
            <div className="token-section">
              <div className="section-header">
                <h3>Informações do Token</h3>
                <div className="badge-container">
                  {tokenInfo && tokenInfo.exp && (
                    <div className="token-badge">
                      Expira em: {new Date(tokenInfo.exp * 1000).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="token-card">
                <h4>Permissões</h4>
                <div className="permissions-list">
                  {tokenInfo && tokenInfo.permissions ? (
                    tokenInfo.permissions.map((permission, idx) => (
                      <div key={idx} className="permission-item">
                        <span className="permission-icon">✓</span>
                        <span className="permission-text">{permission}</span>
                      </div>
                    ))
                  ) : (
                    <div className="no-permissions">Sem permissões definidas</div>
                  )}
                </div>
              </div>
              
              <div className="token-card">
                <h4>Audience</h4>
                <div className="audience-container">
                  {tokenInfo && tokenInfo.aud ? (
                    Array.isArray(tokenInfo.aud) ? (
                      tokenInfo.aud.map((aud, idx) => (
                        <div key={idx} className="audience-item">{aud}</div>
                      ))
                    ) : (
                      <div className="audience-item">{tokenInfo.aud}</div>
                    )
                  ) : (
                    <div className="no-audience">Sem audience definida</div>
                  )}
                </div>
              </div>
              
              <div className="token-card token-raw">
                <div className="token-raw-header">
                  <h4>Token Completo</h4>
                  <button className="copy-button" onClick={() => copyToClipboard(tokenRaw)}>
                    {copied ? 'Copiado!' : 'Copiar'}
                  </button>
                </div>
                <div className="token-box">
                  <pre>{tokenRaw}</pre>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'server':
        return (
          <div className="tab-content">
            {debugResponse && (
              <div className="server-debug">
                <div className="server-card">
                  <h3>Verificação de Acesso</h3>
                  <div className="access-check">
                    {Object.entries(debugResponse.accessCheck || {}).map(([key, value]) => (
                      <div key={key} className="access-item">
                        <span className="access-key">{key}:</span>
                        <span className={`access-value ${value ? 'success' : 'error'}`}>
                          {value ? 'Autorizado' : 'Não Autorizado'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="server-card">
                  <h3>Todas as Permissões Reconhecidas</h3>
                  <div className="permissions-grid">
                    {debugResponse.allPermissions && debugResponse.allPermissions.length > 0 ? (
                      debugResponse.allPermissions.map((permission, idx) => (
                        <div key={idx} className="permission-chip">
                          {permission}
                        </div>
                      ))
                    ) : (
                      <div className="no-permissions">Nenhuma permissão encontrada</div>
                    )}
                  </div>
                </div>
                
                <div className="server-card">
                  <div className="server-raw-header">
                    <h3>Resposta Completa</h3>
                    <button className="copy-button" onClick={() => copyToClipboard(JSON.stringify(debugResponse, null, 2))}>
                      {copied ? 'Copiado!' : 'Copiar'}
                    </button>
                  </div>
                  <div className="server-response">
                    <pre>{JSON.stringify(debugResponse, null, 2)}</pre>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      
      default:
        return <div>Selecione uma aba para visualizar os detalhes.</div>;
    }
  };

  return (
    <div className="auth-debug-container">
      <div className="debug-header">
        <h1 className="debug-title">Diagnóstico Auth0</h1>
        <p className="debug-subtitle">Visualize informações detalhadas sobre sua autenticação e permissões</p>
      </div>
      
      <div className="debug-tabs">
        <button 
          className={`tab-button ${activeTab === 'user' ? 'active' : ''}`} 
          onClick={() => setActiveTab('user')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
            <circle cx="12" cy="7" r="4" />
          </svg>
          <span>Usuário</span>
        </button>
        
        <button 
          className={`tab-button ${activeTab === 'token' ? 'active' : ''}`} 
          onClick={() => setActiveTab('token')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
            <path d="M7 11V7a5 5 0 0 1 10 0v4" />
          </svg>
          <span>Token</span>
        </button>
        
        <button 
          className={`tab-button ${activeTab === 'server' ? 'active' : ''}`} 
          onClick={() => setActiveTab('server')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="2" y="2" width="20" height="8" rx="2" ry="2" />
            <rect x="2" y="14" width="20" height="8" rx="2" ry="2" />
            <line x1="6" y1="6" x2="6.01" y2="6" />
            <line x1="6" y1="18" x2="6.01" y2="18" />
          </svg>
          <span>Servidor</span>
        </button>
      </div>
      
      {renderTabContent()}
    </div>
  );
};

export default AuthDebug; 