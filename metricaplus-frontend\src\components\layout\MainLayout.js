import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import './MainLayout.css';

const MainLayout = ({ children }) => {
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [pageTitle, setPageTitle] = useState('Dashboard');
  const location = useLocation();

  const [expanded, setExpanded] = useState(true);
  const [manuallyToggled, setManuallyToggled] = useState(false);
  // Atualiza o título da página com base na rota atual
  useEffect(() => {
    const path = location.pathname;

    if (path === '/') {
      setPageTitle('Dashboard');
    } else if (path === '/portal') {
      setPageTitle('Portal de Dados');
    } else if (path === '/debug') {
      setPageTitle('Diagnóstico');
    } else if (path === '/settings') {
      setPageTitle('Configurações');
    } else if (path === '/profile') {
      setPageTitle('Meu Perfil');
    } else if (path === '/users') {
      setPageTitle('Usuários');
    } else if (path === '/analytics') {
      setPageTitle('Analytics');
    } else if (path === '/reports') {
      setPageTitle('Relatórios');
    } else {
      setPageTitle('MetricaPlus');
    }
  }, [location]);

  // Comunicação com a Sidebar para saber quando ela é expandida/contraída
  const handleSidebarToggle = (expanded) => {
    setSidebarExpanded(expanded);
  };

  return (
    <div className="app-container">
      <Sidebar onToggle={handleSidebarToggle}
        expanded={expanded} setExpanded={setExpanded}
        manuallyToggled={manuallyToggled} setManuallyToggled={setManuallyToggled}
      />

      <div className={`main-content ${sidebarExpanded ? 'sidebar-expanded' : 'sidebar-collapsed'}`}>
        <Header pageTitle={pageTitle}
          expanded={expanded} setExpanded={setExpanded}
          manuallyToggled={manuallyToggled} setManuallyToggled={setManuallyToggled}
        />

        <main className="content-area">
          {children}
        </main>

        <footer className="app-footer">
          <div className="footer-content">
            <div className="footer-copyright">
              © {new Date().getFullYear()} MetricaPlus - Todos os direitos reservados
            </div>
            <div className="footer-links">
              <a href="#" className="footer-link">Termos de Uso</a>
              <a href="#" className="footer-link">Política de Privacidade</a>
              <a href="#" className="footer-link">Suporte</a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default MainLayout; 