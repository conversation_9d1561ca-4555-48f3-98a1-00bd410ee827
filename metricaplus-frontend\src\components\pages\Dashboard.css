.dashboard-container {
  padding: 1rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* KPIs Row */
.kpis-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  width: 100%;
}

.kpi-card {
  background: var(--kpi-color);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--kpi-color);
  z-index: 1;
}

.kpi-card > * {
  position: relative;
  z-index: 2;
}

.kpi-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.kpi-icon {
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.kpi-content {
  flex: 1;
}

.kpi-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Charts Row */
.charts-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  width: 100%;
  flex: 1;
}

.chart-container {
  background: var(--card-color);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  min-width: 0;
  height: 565px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: var(--shadow-lg);
}

/* Título dos gráficos */
.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

/* Garantir que os gráficos ECharts se redimensionem */
.chart-container > div:not(.chart-title) {
  width: 100% !important;
  height: 480px !important;
}

.chart-container canvas {
  max-width: 100% !important;
  height: auto !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-row {
    grid-template-columns: 1fr;
  }
  
  .kpis-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .chart-container > div:not(.chart-title) {
    width: 100% !important;
    height: 350px !important;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .kpis-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .kpi-card {
    padding: 1.5rem;
  }
  
  .kpi-icon {
    font-size: 2.5rem;
    padding: 0.75rem;
  }
  
  .kpi-value {
    font-size: 2.5rem;
  }
  
  .charts-row {
    gap: 1rem;
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    padding: 1rem;
  }
  
  .chart-container > div:not(.chart-title) {
    width: 100% !important;
    height: 300px !important;
  }
}

@media (max-width: 480px) {
  .kpi-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .kpi-title {
    font-size: 1rem;
  }
  
  .kpi-value {
    font-size: 2rem;
  }
} 